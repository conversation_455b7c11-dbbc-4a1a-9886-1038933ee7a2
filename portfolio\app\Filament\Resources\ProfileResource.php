<?php

namespace App\Filament\Resources;

use App\Filament\Resources\ProfileResource\Pages;
use App\Models\Profile;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class ProfileResource extends Resource
{
    protected static ?string $model = Profile::class;
    protected static ?string $navigationIcon = 'heroicon-o-user';
    protected static ?string $navigationGroup = 'Portfolio';
    protected static ?int $navigationSort = 1;
    protected static ?string $modelLabel = 'My Profile';
    protected static bool $shouldRegisterNavigation = true;

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Hidden::make('user_id')
                    ->default(auth()->id()),

                Forms\Components\Section::make('Basic Information')
                    ->schema([
                        Forms\Components\TextInput::make('title')
                            ->label('Job Title')
                            ->placeholder('Senior Software Engineer')
                            ->maxLength(255),
                        Forms\Components\FileUpload::make('image')
                            ->label('Profile Photo')
                            ->image()
                            ->directory('profile-photos'),
                        Forms\Components\TextInput::make('tagline')
                            ->placeholder('Full-stack Developer | Laravel Expert')
                            ->maxLength(255),
                        Forms\Components\Textarea::make('description')
                            ->label('About Me')
                            ->rows(4),
                    ])->columns(2),

                Forms\Components\Section::make('Contact Information')
                    ->schema([
                        Forms\Components\TextInput::make('phone')
                            ->tel()
                            ->maxLength(255),
                        Forms\Components\Textarea::make('address')
                            ->rows(2),
                        Forms\Components\TextInput::make('location_city')
                            ->label('City'),
                        Forms\Components\TextInput::make('location_country')
                            ->label('Country'),
                    ])->columns(2),

                Forms\Components\Section::make('Professional Details')
                    ->schema([
                        Forms\Components\FileUpload::make('resume_pdf')
                            ->label('Resume/CV')
                            ->acceptedFileTypes(['application/pdf'])
                            ->directory('resumes'),
                        Forms\Components\TextInput::make('years_of_experience')
                            ->numeric()
                            ->minValue(0)
                            ->maxValue(50)
                            ->suffix('years'),
                        Forms\Components\Select::make('availability_status')
                            ->options([
                                'available' => 'Available for Work',
                                'not_available' => 'Not Available',
                            ])
                            ->default('available'),
                        Forms\Components\TextInput::make('availability_text')
                            ->placeholder('Available for freelance projects')
                            ->maxLength(255),
                    ])->columns(2),

                Forms\Components\Section::make('Skills & Technologies')
                    ->schema([
                        Forms\Components\Select::make('skills')
                            ->relationship(
                                name: 'skills',
                                titleAttribute: 'name',
                                modifyQueryUsing: fn ($query) => $query
                                    ->where('is_core_technology', true)
                                    ->orWhere('is_highlighted', true)
                                    ->orderBy('skills.order')
                            )
                            ->multiple()
                            ->preload()
                            ->searchable()
                            ->columnSpanFull()
                            ->createOptionForm([
                                Forms\Components\Hidden::make('user_id')
                                    ->default(auth()->id()),

                                Forms\Components\Section::make('Skill Information')
                                    ->description('Configure the basic skill information.')
                                    ->schema([
                                        Forms\Components\TextInput::make('name')
                                            ->required()
                                            ->maxLength(255),
                                        Forms\Components\FileUpload::make('img')
                                            ->label('Skill Icon')
                                            ->image()
                                            ->directory('skill-icons')
                                            ->columnSpanFull(),
                                        Forms\Components\Select::make('type')
                                            ->options([
                                                'technology' => 'Technology',
                                                'language' => 'Programming Language',
                                                'framework' => 'Framework',
                                                'tool' => 'Development Tool',
                                                'soft_skill' => 'Soft Skill',
                                            ])
                                            ->required()
                                            ->default('technology')
                                            ->native(false),
                                        Forms\Components\TextInput::make('proficiency')
                                            ->label('Skill Level (%)')
                                            ->numeric()
                                            ->minValue(0)
                                            ->maxValue(100)
                                            ->step(5)
                                            ->default(50)
                                            ->suffix('%'),
                                    ])->columns(2),

                                Forms\Components\Section::make('Skill Categories')
                                    ->description('Define how this skill should be displayed.')
                                    ->schema([
                                        Forms\Components\Grid::make(3)
                                            ->schema([
                                                Forms\Components\Toggle::make('is_core_technology')
                                                    ->label('Core Technology')
                                                    ->helperText('Show in profile as main technology')
                                                    ->default(false),
                                                Forms\Components\Toggle::make('is_highlighted')
                                                    ->label('Highlight Skill')
                                                    ->helperText('Feature this skill prominently')
                                                    ->default(false),
                                                Forms\Components\Toggle::make('is_project')
                                                    ->label('Project Technology')
                                                    ->helperText('Available for project tech stack')
                                                    ->default(false),
                                            ]),
                                    ]),

                                Forms\Components\Hidden::make('order')
                                    ->default(fn () => \App\Models\Skill::where('user_id', auth()->id())->max('order') + 1),
                            ])
                            ->columns(2),
                    ]),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\ImageColumn::make('image')
                    ->label('Photo')
                    ->circular(),
                Tables\Columns\TextColumn::make('title')
                    ->searchable(),
                Tables\Columns\TextColumn::make('full_location')
                    ->label('Location'),
                Tables\Columns\TextColumn::make('years_experience_text')
                    ->label('Experience'),
                Tables\Columns\BadgeColumn::make('availability_status')
                    ->colors([
                        'success' => 'available',
                        'danger' => 'not_available',
                    ]),
                Tables\Columns\TextColumn::make('updated_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('availability_status')
                    ->options([
                        'available' => 'Available',
                        'not_available' => 'Not Available',
                    ]),
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListProfiles::route('/'),
            'create' => Pages\CreateProfile::route('/create'),
            'edit' => Pages\EditProfile::route('/{record}/edit'),
        ];
    }

    public static function getEloquentQuery(): Builder
    {
        return parent::getEloquentQuery()
            ->where('user_id', auth()->id());
    }
}
