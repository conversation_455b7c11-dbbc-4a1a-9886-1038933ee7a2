<?php

        namespace App\Filament\Pages;

        use Filament\Pages\Dashboard as BaseDashboard;
        use Illuminate\Contracts\Support\Htmlable;
        use Filament\Actions\Action;
        use Filament\Forms\Components\TextInput;
        use Filament\Notifications\Notification;
        use Illuminate\Support\Facades\Hash;

        class Dashboard extends BaseDashboard
        {
            protected static ?string $navigationGroup = 'Fitur Dashboard';

            protected static ?string $navigationIcon = 'heroicon-o-home';

            protected static ?int $navigationSort = 1;

            public function getTitle(): string | Htmlable
            {
                return 'Dashboard';
            }

            protected function getHeaderActions(): array
            {
                return [
                    Action::make('changePassword')
                        ->label('Change Password')
                        ->icon('heroicon-o-key')
                        ->modalHeading('Change Your Password')
                        ->form([
                            TextInput::make('current_password')
                                ->label('Current Password')
                                ->password()
                                ->required()
                                ->rule('current_password'),

                            TextInput::make('new_password')
                                ->label('New Password')
                                ->password()
                                ->required()
                                ->minLength(8)
                                ->rules(['confirmed']),

                            TextInput::make('new_password_confirmation')
                                ->label('Confirm New Password')
                                ->password()
                                ->required(),
                        ])
                        ->action(function (array $data): void {
                            auth()->user()->update([
                                'password' => Hash::make($data['new_password']),
                            ]);

                            Notification::make()
                                ->success()
                                ->title('Password updated successfully')
                                ->send();
                        }),
                ];
            }

            protected function getHeaderWidgets(): array
            {
                return [
                    \Filament\Widgets\AccountWidget::class,
                ];
            }

            protected function getFooterWidgets(): array
            {
                return [
                ];
            }

            public function getColumns(): int | array
            {
                return 2;
            }
        }
