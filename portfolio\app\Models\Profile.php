<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;

class Profile extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'user_id',
        'title',
        'description',
        'phone',
        'address',
        'image',
        'resume_pdf',
        'tagline',
        'availability_status',
        'availability_text',
        'location_city',
        'location_country',
        'years_of_experience',
    ];

    protected $attributes = [
        'availability_status' => 'available',
        'availability_text' => 'Available for Work',
        'title' => 'Welcome to My Portfolio',
        'years_of_experience' => 0,
        'description' => 'Welcome to my portfolio website',
        'tagline' => 'Full Stack Developer',
    ];

    protected $casts = [
        'years_of_experience' => 'integer',
    ];

    /**
     * Get the user that owns the profile.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function skills(): BelongsToMany
    {
        return $this->belongsToMany(Skill::class, 'profile_skill')
                    ->withTimestamps()
                    ->withPivot('order')
                    ->orderBy('profile_skill.order');
    }

    public function coreTechnologies()
    {
        return $this->belongsToMany(Skill::class, 'profile_skill')
                    ->withTimestamps()
                    ->withPivot('order')
                    ->where('is_core_technology', true)
                    ->orderBy('profile_skill.order');
    }

    public function highlightedSkills()
    {
        return $this->belongsToMany(Skill::class, 'profile_skill')
                    ->withTimestamps()
                    ->withPivot('order')
                    ->where('is_highlighted', true)
                    ->orderBy('profile_skill.order');
    }

    public function projectSkills()
    {
        return $this->belongsToMany(Skill::class, 'profile_skill')
                    ->withTimestamps()
                    ->withPivot('order')
                    ->where('is_project', true)
                    ->orderBy('profile_skill.order');
    }

    public function skillsByType($type)
    {
        return $this->belongsToMany(Skill::class, 'profile_skill')
                    ->withTimestamps()
                    ->withPivot('order')
                    ->where('type', $type)
                    ->orderBy('profile_skill.order');
    }

    public function getFullLocationAttribute(): string
    {
        return collect([$this->location_city, $this->location_country])
            ->filter()
            ->join(', ');
    }

    public function getIsAvailableAttribute(): bool
    {
        return $this->availability_status === 'available';
    }

    public function getYearsExperienceTextAttribute(): string
    {
        $years = $this->years_of_experience;
        return $years . ' ' . str('year')->plural($years) . ' of experience';
    }

    public function scopeAvailable($query)
    {
        return $query->where('availability_status', 'available');
    }

    public function scopeByLocation($query, $city = null, $country = null)
    {
        return $query->when($city, fn($q) => $q->where('location_city', $city))
                    ->when($country, fn($q) => $q->where('location_country', $country));
    }
}
