<section id="services" class="lg:py-10 py-5 relative overflow-hidden">
    <!-- Background Elements -->
    <div class="absolute inset-0" style="z-index: -1;">
        <div class="absolute inset-0 bg-gradient-to-br from-gray-50 to-white"></div>
        <div class="absolute inset-0 bg-[linear-gradient(to_right,#80808012_1px,transparent_1px),linear-gradient(to_bottom,#80808012_1px,transparent_1px)] bg-[size:24px_24px]"></div>
    </div>

    <div class="max-w-[1200px] mx-auto px-4">
        <!-- Section Header -->
        <div class="text-center mb-4">
            <div class="inline-flex items-center gap-2 px-4 py-2 rounded-full bg-white/80 backdrop-blur shadow-lg mb-4">
                <span class="relative flex h-2 w-2">
                    <span class="animate-ping absolute inline-flex h-full w-full rounded-full bg-emerald-400 opacity-75"></span>
                    <span class="relative inline-flex rounded-full h-2 w-2 bg-emerald-500"></span>
                </span>
                <span class="text-sm font-medium bg-gradient-to-r from-emerald-600 to-teal-600 bg-clip-text text-transparent">
                    My Services
                </span>
            </div>
            <h2 class="lg:text-3xl text-2xl font-bold text-gray-900 mb-2">What I Can Do For You</h2>
            <p class="text-gray-600 max-w-2xl mx-auto">
                I offer a comprehensive range of professional services to help bring your vision to life
            </p>
        </div>

        <!-- Services Grid -->

        <!-- Services Grid -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            @foreach($services->where('is_active', true) as $service)
            <div class="group relative" data-aos="fade-up" data-aos-delay="{{ $loop->index * 100 }}">
                <!-- Card -->
                <div class="relative bg-white rounded-2xl shadow-lg transition-all duration-300
                            hover:shadow-xl border border-emerald-500 hover:border-emerald-100
                            h-full flex flex-col overflow-hidden">

                    <!-- Service Image -->
                    <div class="relative h-48">
                        @if($service->image)
                            <img src="{{ Storage::url($service->image) }}"
                                 alt="{{ $service->name }}"
                                 class="w-full h-full object-cover">
                        @else
                            <div class="w-full h-full bg-gradient-to-br from-emerald-50 to-teal-50 flex items-center justify-center">
                                <svg class="w-12 h-12 text-emerald-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                          d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"/>
                                </svg>
                            </div>
                        @endif

                        <!-- Category Badge -->
                        <span class="absolute top-4 left-4 px-3 py-1 rounded-full text-xs font-medium
                                   bg-teal-100/90 text-teal-700">
                            {{ $service->category }}
                        </span>
                    </div>

                    <div class="p-6 flex flex-col flex-grow">
                        <!-- Service Header -->
                        <h3 class="text-xl font-bold text-gray-900 mb-3">
                            {{ $service->name }}
                        </h3>

                        <!-- Service Info -->
                        <div class="space-y-3 mb-4">
                            <!-- Price -->
                            <div class="inline-block bg-emerald-50 rounded-lg px-4 py-2">
                                <div class="text-xs text-emerald-600 font-medium mb-1">Start From</div>
                                <div class="text-lg font-bold text-emerald-700">
                                    {{ $service->formatted_price }}
                                </div>
                            </div>

                            <!-- Delivery Time -->
                            <div class="flex items-center text-sm text-gray-600">
                                <svg class="w-4 h-4 text-emerald-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"/>
                                </svg>
                                <span>Estimated Time: {{ $service->delivery_time }}</span>
                            </div>
                        </div>

                        <!-- Description Preview -->
                        <div class="prose prose-sm text-gray-600 mb-6 line-clamp-3">
                            {!! $service->description !!}
                        </div>

                        <!-- Action Buttons -->
                        <div class="flex gap-2 mt-auto">
                            <button
                                onclick="scrollToHireForm('{{ $service->id }}', '{{ $service->name }}')"
                                class="flex-1 inline-flex items-center justify-center px-4 py-2.5
                                       border border-transparent rounded-lg text-sm font-medium
                                       text-white bg-emerald-600 hover:bg-emerald-700
                                       focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-emerald-500
                                       transition-colors duration-200">
                                Hire Me
                            </button>
                            <button
                                onclick="openServiceModal('{{ $service->id }}')"
                                class="inline-flex items-center justify-center px-4 py-2.5
                                       border border-gray-300 rounded-lg text-sm font-medium
                                       text-gray-700 bg-white hover:bg-gray-50
                                       focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-emerald-500
                                       transition-colors duration-200">
                                Read More
                            </button>
                        </div>
                    </div>
                </div>
            </div>
            @endforeach
        </div>
    </div>

    <!-- Modal Container -->
    <div id="serviceModal" class="fixed inset-0 z-50 hidden">
        <!-- Backdrop -->
        <div class="fixed inset-0 transition-opacity duration-300 ease-out"
             id="modalBackdrop"
             onclick="closeServiceModal()">
            <div class="absolute inset-0 bg-black bg-opacity-50 backdrop-blur-sm opacity-0"></div>
        </div>

        <!-- Modal Wrapper -->
        <div class="fixed inset-0 overflow-y-auto">
            <div class="flex min-h-screen items-center justify-center p-4">
                <!-- Modal Content -->
                <div class="relative bg-white rounded-2xl shadow-xl w-full max-w-3xl
                            opacity-0 translate-y-4 transition-all duration-300 ease-out
                            flex flex-col"
                     id="modalContent"
                     style="max-height: 85vh;">

                    <!-- Close Button (Top) -->
                    <button
                        onclick="closeServiceModal()"
                        class="absolute right-4 top-4 text-gray-400 hover:text-gray-500
                               focus:outline-none z-10">
                        <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                        </svg>
                    </button>

                        <!-- Scrollable Content -->
                        <div class="flex-1 overflow-y-auto p-6 scrollbar-hide">
                            <h3 id="modalTitle" class="lg:text-xl text-lg font-bold mb-6 pr-8"></h3>

                        <!-- Service Info -->
                        <div class="flex flex-wrap gap-4 mb-6">
                            <div class="bg-emerald-50 rounded-lg px-4 py-2">
                                <div class="text-xs text-emerald-600 font-medium mb-1">Start From</div>
                                <div id="modalPrice" class="text-lg font-bold text-emerald-700"></div>
                            </div>
                            <div class="bg-gray-50 rounded-lg px-4 py-2">
                                <div class="text-xs text-gray-600 font-medium mb-1">Estimated Time</div>
                                <div id="modalDelivery" class="text-lg font-medium text-gray-700"></div>
                            </div>
                        </div>

                        <!-- Description -->
                        <div class="prose prose-emerald max-w-none" id="modalDescription"></div>
                    </div>

                    <!-- Footer with Close Button -->
                    <div class="border-t p-4 bg-gray-50 mt-auto">
                        <button
                            onclick="closeServiceModal()"
                            class="w-auto inline-flex items-center justify-center px-6 py-3
                                   rounded-lg text-sm font-medium text-gray-700
                                   bg-white border border-gray-300 hover:bg-gray-50
                                   focus:outline-none focus:ring-2 focus:ring-offset-2
                                   focus:ring-emerald-500 transition-colors duration-200">
                            Close
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>



@push('scripts')
<script>
let cachedServices = {};

function scrollToHireForm(serviceId, serviceName) {
    const hireSection = document.getElementById('hire');
    const serviceSelect = document.querySelector('select[name="service_id"]');

    if (hireSection) {
        const offset =50; // Tambahkan margin 100px dari atas
        const elementPosition = hireSection.getBoundingClientRect().top;
        const offsetPosition = elementPosition + window.pageYOffset - offset;

        window.scrollTo({
            top: offsetPosition,
            behavior: 'smooth'
        });

        if (serviceSelect) {
            serviceSelect.value = serviceId;
        }
    }
}

// Cache for storing fetched service data
const serviceCache = new Map();

// Preload spinner HTML
const spinnerHTML = `
    <div class="flex justify-center py-8">
        <div class="animate-spin rounded-full h-8 w-8 border-2 border-emerald-500 border-t-transparent"></div>
    </div>
`;

async function openServiceModal(serviceId) {
    // Show modal immediately with loading state
    const modal = document.getElementById('serviceModal');
    const backdrop = modal.querySelector('#modalBackdrop div');
    const content = document.getElementById('modalContent');
    const description = document.getElementById('modalDescription');

    // Clear previous content and show loading state
    description.innerHTML = spinnerHTML;
    document.getElementById('modalTitle').textContent = 'Loading...';
    document.getElementById('modalPrice').textContent = '...';
    document.getElementById('modalDelivery').textContent = '...';

    // Show modal with animation
    modal.classList.remove('hidden');
    requestAnimationFrame(() => {
        backdrop.classList.add('opacity-100');
        content.classList.remove('opacity-0', 'translate-y-4');
    });

    try {
        let serviceData;

        // Check cache first
        if (serviceCache.has(serviceId)) {
            serviceData = serviceCache.get(serviceId);
        } else {
            // Fetch data
            const response = await fetch(`/api/services/${serviceId}`);
            if (!response.ok) throw new Error('Network response was not ok');
            serviceData = await response.json();

            // Cache the result
            serviceCache.set(serviceId, serviceData);
        }

        // Update modal content
        requestAnimationFrame(() => {
            document.getElementById('modalTitle').textContent = serviceData.name;
            document.getElementById('modalPrice').textContent = serviceData.formatted_price;
            document.getElementById('modalDelivery').textContent = serviceData.delivery_time;

            // Create temporary div to parse HTML content
            const tempDiv = document.createElement('div');
            tempDiv.innerHTML = serviceData.description;

            // Replace content with parsed HTML
            description.innerHTML = '';
            description.appendChild(tempDiv);
        });

    } catch (error) {
        console.error('Error loading service:', error);
        description.innerHTML = `
            <div class="text-red-500 text-center py-4">
                Failed to load service details. Please try again.
            </div>
        `;
    }
}

function closeServiceModal() {
    const modal = document.getElementById('serviceModal');
    const backdrop = modal.querySelector('#modalBackdrop div');
    const content = document.getElementById('modalContent');

    backdrop.classList.remove('opacity-100');
    content.classList.add('opacity-0', 'translate-y-4');

    setTimeout(() => modal.classList.add('hidden'), 300);
}

// Close on escape key
document.addEventListener('keydown', e => {
    if (e.key === 'Escape') closeServiceModal();
});

// Optional: Preload frequently accessed services
document.addEventListener('DOMContentLoaded', () => {
    // Get visible service IDs from your page
    const visibleServiceIds = Array.from(document.querySelectorAll('[data-service-id]'))
        .map(el => el.dataset.serviceId)
        .slice(0, 3); // Preload first 3 services

    // Preload data in the background
    visibleServiceIds.forEach(id => {
        fetch(`/api/services/${id}`)
            .then(response => response.json())
            .then(data => serviceCache.set(id, data))
            .catch(() => {}); // Silently fail on preload
    });
});
</script>

<style>
/* Essential transitions only */
.transition-all {
    transition-property: all;
}

.duration-300 {
    transition-duration: 300ms;
}

.ease-out {
    transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
}

/* Essential modal styles */
.prose ul {
    list-style-type: disc;
    padding-left: 1.5em;
}

.prose ol {
    list-style-type: decimal;
    padding-left: 1.5em;
}

/* Hide scrollbar but maintain functionality */
.scrollbar-hide {
    -ms-overflow-style: none;  /* IE and Edge */
    scrollbar-width: none;     /* Firefox */
}

.scrollbar-hide::-webkit-scrollbar {
    display: none;             /* Chrome, Safari and Opera */
}

/* Ensure modal is vertically centered */
.min-h-screen {
    min-height: 100vh;
}

#modalContent {
    margin: 0 auto;
}
</style>
@endpush

