<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('profiles', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->cascadeOnDelete();
            $table->string('title')->nullable();
            $table->text('description')->nullable();
            $table->string('phone')->nullable();
            $table->string('email')->nullable();
            $table->text('address')->nullable();
            $table->string('image')->nullable();
            $table->string('resume_pdf')->nullable();
            $table->string('tagline')->nullable();
            $table->enum('availability_status', ['available', 'not_available'])->default('available');
            $table->string('availability_text')->nullable();
            $table->string('location_city')->nullable();
            $table->string('location_country')->nullable();
            $table->integer('years_of_experience')->default(0);
            $table->timestamps();
            $table->softDeletes();

            $table->unique(['user_id', 'deleted_at']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('profiles');
    }
};
