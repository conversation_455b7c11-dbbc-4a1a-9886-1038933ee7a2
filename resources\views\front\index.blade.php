@extends('layouts.frontend')

@section('content')

<body>
    {{-- hero section --}}
    <section class="relative max-w-[1200px] mx-auto px-4 min-h-screen flex items-center bg-gradient-to-br from-gray-50 to-white">
        <div class="container mx-auto py-12 relative z-10">
            <div class="flex flex-col lg:flex-row items-center gap-12">
                <!-- Text Content -->
                <div class="lg:w-1/2 text-center lg:text-left w-full mx-auto">
                    <div class="space-y-6">
                        <!-- Availability Badge -->
                        @if($profile)
                        <div class="inline-flex items-center gap-2 px-4 py-2 rounded-full backdrop-blur-sm {{ $profile->availability_status === 'available' ? 'bg-emerald-100/80' : 'bg-yellow-100/80' }}">
                            <span class="relative flex h-2 w-2">
                                <span class="animate-ping absolute inline-flex h-full w-full rounded-full {{ $profile->availability_status === 'available' ? 'bg-emerald-400' : 'bg-yellow-400' }} opacity-75"></span>
                                <span class="relative inline-flex rounded-full h-2 w-2 {{ $profile->availability_status === 'available' ? 'bg-emerald-500' : 'bg-yellow-500' }}"></span>
                            </span>
                            <span class="text-sm font-medium {{ $profile->availability_status === 'available' ? 'text-emerald-700' : 'text-yellow-700' }}">
                                {{ $profile->availability_text ?? 'Available for Work' }}
                            </span>
                        </div>

                        <h1 class="text-2xl lg:text-4xl font-bold text-gray-900 leading-tight animate-fade-in">
                            {{ $profile->title ?? 'Welcome to My Portfolio' }}
                        </h1>
                        @else
                        <!-- Default Content when no profile exists -->
                        <div class="inline-flex items-center px-4 py-2 rounded-full bg-green-100 text-green-800 text-sm font-medium animate-fade-in">
                            <span class="w-2 h-2 rounded-full bg-green-500 mr-2"></span>
                            Available for Work
                        </div>

                        <h1 class="text-2xl lg:text-4xl font-bold text-gray-800 leading-tight animate-fade-in">
                            Welcome to My Portfolio
                        </h1>
                        @endif

                        <!-- Mobile Image - Hanya muncul di mobile -->
                        <div class="block lg:hidden relative w-full mb-8">
                            <div class="relative w-[80%] aspect-square mx-auto">
                                <!-- Background Circles -->
                                <div class="absolute inset-0 bg-gradient-to-r from-primary/10 to-secondary/10 rounded-full blur-3xl"></div>

                                <!-- Profile Image -->
                                <div class="relative w-full h-full rounded-full border-8 border-white shadow-2xl overflow-hidden animate-float">
                                    @if($profile->image)
                                    <img src="{{ Storage::url($profile->image) }}"
                                        alt="Profile"
                                        class="w-full h-full object-cover">
                                    @else
                                    <div class="w-full h-full bg-gray-100 flex items-center justify-center">
                                        <span class="text-2xl font-bold text-gray-400">
                                            {{ substr($profile->image ?? 'Update Your Profile', 0, 1 ) }}
                                        </span>
                                    </div>
                                    @endif
                                </div>
                            </div>
                        </div>

                        <p class="text-xl text-gray-600 leading-relaxed max-w-2xl animate-fade-in-up">
                            {{ $profile->tagline }}
                        </p>

                        <!-- Quick Info -->
                        <div class="flex flex-wrap justify-center lg:justify-start gap-4 text-gray-600 text-sm animate-fade-in-up" style="animation-delay: 100ms">
                            @if($profile->location_city && $profile->location_country)
                            <div class="flex items-center">
                                <svg class="w-5 h-5 mr-2 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                                </svg>
                                <span>{{ $profile->location_city }}, {{ $profile->location_country }}</span>
                            </div>
                            @endif

                            @if($profile->years_of_experience)
                            <div class="flex items-center">
                                <svg class="w-5 h-5 mr-2 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 13.255A23.931 23.931 0 0112 15c-3.183 0-6.22-.62-9-1.745M16 6V4a2 2 0 00-2-2h-4a2 2 0 00-2 2v2m4 6h.01M5 20h14a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                                </svg>
                                <span>{{ $profile->years_of_experience }}+ Years Experience</span>
                            </div>
                            @endif

                            @if($profile->phone)
                            <div class="flex items-center">
                                <svg class="w-5 h-5 mr-2 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z" />
                                </svg>
                                <span>{{ $profile->phone }}</span>
                            </div>
                            @endif
                        </div>

                        <!-- Description -->
                        <p class="text-gray-600 leading-relaxed animate-fade-in-up" style="animation-delay: 200ms">
                            {{ $profile->description }}
                        </p>

                        <!-- Action Buttons -->
                        <div class="flex flex-wrap gap-3 justify-center lg:justify-start">
                            <a href="mailto:{{ $profile->email }}"
                                class="group inline-flex items-center px-4 sm:px-5 py-2.5 rounded-full bg-white shadow-lg hover:shadow-xl border border-indigo-600 font-medium transition-all duration-300 animate-fade-in-up text-sm sm:text-base"
                                style="animation-delay: 300ms">
                                <span class="bg-gradient-to-r from-violet-600 to-indigo-600 bg-clip-text text-transparent">Let's Talk</span>
                                <div class="w-7 h-7 sm:w-8 sm:h-8 ml-2 rounded-full bg-violet-50 flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                                    <svg class="w-3.5 h-3.5 sm:w-4 sm:h-4 text-violet-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14 5l7 7m0 0l-7 7m7-7H3" />
                                    </svg>
                                </div>
                            </a>

                            @if($profile->resume_pdf)
                            <a href="{{ Storage::url($profile->resume_pdf) }}"
                                target="_blank"
                                class="group inline-flex items-center px-4 sm:px-5 py-2.5 rounded-full bg-white shadow-lg hover:shadow-xl border border-emerald-500 font-medium transition-all duration-300 animate-fade-in-up text-sm sm:text-base"
                                style="animation-delay: 400ms">
                                <div class="w-7 h-7 sm:w-8 sm:h-8 mr-2 rounded-full bg-emerald-50 flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                                    <svg class="w-3.5 h-3.5 sm:w-4 sm:h-4 text-emerald-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                                    </svg>
                                </div>
                                <span class="bg-gradient-to-r from-emerald-600 to-teal-600 bg-clip-text text-transparent">Download CV</span>
                            </a>
                            @endif
                        </div>

                        <!-- Core Technologies -->
                        <div class="pt-6 animate-fade-in-up" style="animation-delay: 500ms">
                            <h3 class="text-sm font-semibold text-gray-400 uppercase tracking-wider mb-4 flex items-center">
                                <span class="mr-2 ml-1 lg:ml-0">Core Technologies</span>
                                <div class="flex-grow h-px bg-gradient-to-r from-gray-200 to-transparent"></div>
                            </h3>
                            <div class="splide" id="core-technologies-slider">
                                <div class="splide__track">
                                    <div class="splide__list">
                                        @foreach($profile->coreTechnologies()->get() as $skill)
                                        <div class="splide__slide !w-auto">
                                            <div class="group flex items-center px-4 py-2.5 bg-white border border-blue-600 rounded-lg hover:border-primary/40 hover:bg-primary/5 transition-all duration-300 shadow-sm hover:shadow-md">
                                                @if($skill->img)
                                                <div class="relative w-6 h-6 mr-2.5">
                                                    <div class="absolute inset-0 bg-gradient-to-b from-transparent to-white/10 rounded-full group-hover:scale-110 transition-transform duration-300"></div>
                                                    <img src="{{ Storage::url($skill->img) }}"
                                                        alt="{{ $skill->name }}"
                                                        class="w-6 h-6 object-contain relative z-10">
                                                </div>
                                                @endif
                                                <span class="font-medium text-sm text-gray-700">{{ $skill->name }}</span>
                                            </div>
                                        </div>
                                        @endforeach
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Desktop Image - Hanya muncul di desktop -->
                <div class="hidden lg:block lg:w-1/2 relative">
                    <div class="relative w-[80%] aspect-square mx-auto">
                        <!-- Background Circles -->
                        <div class="absolute inset-0 bg-gradient-to-r from-primary/10 to-secondary/10 rounded-full blur-3xl"></div>

                        <!-- Profile Image -->
                        <div class="relative w-full h-full rounded-full overflow-hidden">
                            @if($profile->image)
                            <img src="{{ Storage::url($profile->image) }}"
                                alt="Profile"
                                class="w-full h-full object-cover">
                            @else
                            <div class="w-full h-full bg-gray-100 flex items-center justify-center">
                                <span class="text-4xl font-bold text-gray-400">
                                    {{ substr($profile->title ?? 'Web Developer', 0, 1) }}
                                </span>
                            </div>
                            @endif
                        </div>

                        <!-- Desktop Tech Bubbles -->
                        @php
                        $positions = [
                        'top: 15%; left: -15px;', // Kiri atas
                        'top: 15%; right: -15px;', // Kanan atas
                        'bottom: 15%; right: -15px;', // Kanan bawah
                        'bottom: 15%; left: -15px;', // Kiri bawah
                        ];
                        @endphp

                        @foreach($profile->skills()->where('is_highlighted', true)->orderBy('skills.order')->take(4)->get() as $index => $skill)
                        <div class="absolute animate-float tech-bubble bg-white rounded-full shadow-lg p-2 transition-all duration-300 hover:scale-110 z-10 border border-blue-600"
                            style="{{ $positions[$index] ?? '' }}">
                            @if($skill->img)
                            <img src="{{ Storage::url($skill->img) }}"
                                alt="{{ $skill->name }}"
                                class="w-8 h-8 xl:w-10 xl:h-10 object-contain rounded-fl" ul
                                title="{{ $skill->name }}">
                            @else
                            <div class="w-8 h-8 xl:w-10 xl:h-10 rounded-full bg-gray-100 flex items-center justify-center font-medium text-gray-600">
                                {{ substr($skill->name, 0, 2) }}
                            </div>
                            @endif
                        </div>
                        @endforeach
                    </div>
                </div>
            </div>
        </div>

        <!-- Scroll Indicator - Desktop -->
        <div class="absolute -bottom-6 left-0 right-0 flex justify-center items-center hidden md:flex" style="z-index: 20;">
            <button onclick="scrollToServices()" class="p-2.5 bg-white/80 backdrop-blur-sm rounded-full shadow-lg hover:bg-white hover:shadow-xl transition-all duration-300 cursor-pointer animate-bounce">
                <svg class="w-5 h-5 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 14l-7 7m0 0l-7-7m7 7V3"></path>
                </svg>
            </button>
        </div>

        <!-- Mobile Scroll Indicator -->
        <div class="absolute -bottom-4 left-0 right-0 flex justify-center items-center md:hidden" style="z-index: 20;">
            <button onclick="scrollToServices()" class="p-2 bg-white/80 backdrop-blur-sm rounded-full shadow-lg hover:bg-white hover:shadow-xl transition-all duration-300 animate-bounce">
                <svg class="w-5 h-5 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 14l-7 7m0 0l-7-7m7 7V3"></path>
                </svg>
            </button>
        </div>
    </section>
</body>
{{-- end hero section --}}


{{-- end stats section --}}{{-- stats section --}}
<section class="relative py-8 overflow-hidden">
    <!-- Background Lottie Animations -->
    <div class="absolute inset-0 pointer-events-none opacity-10">
        <div class="absolute top-0 left-0 w-40">
            <lottie-player
                src="https://lottie.host/8f023711-cc77-4b70-be84-628895a1376a/jHVXoMX8kk.json"
                background="transparent"
                speed="1"
                loop
                autoplay>
            </lottie-player>
        </div>
        <div class="absolute bottom-0 right-0 w-40">
            <lottie-player
                src="https://lottie.host/17c591c7-77cd-4670-a36a-ef66dc5250dd/22PQYxi9dZ.json"
                background="transparent"
                speed="1"
                loop
                autoplay>
            </lottie-player>
        </div>
    </div>

    <div class="max-w-6xl mx-auto px-4">
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 justify-items-center relative z-10">
            <!-- Projects -->
            <div class="relative">
                <div class="absolute inset-0 w-full h-full opacity-[0.5] lg:opacity-100 lg:w-11 lg:h-11 lg:left-4 lg:top-4 lg:inset-auto
                            group-hover:opacity-100 transition-all duration-300">
                    <lottie-player
                        src="https://lottie.host/582d70ad-c11e-4c95-83c4-1bd690a661ca/JrbyIMVxRt.json"
                        background="transparent"
                        speed="1"
                        loop
                        autoplay>
                    </lottie-player>
                </div>
                <div class="group bg-white p-4 rounded-xl hover:shadow-lg hover:bg-gray-50/80 transition-all w-64 sm:w-full relative z-10">
                    <div class="flex flex-col sm:flex-row items-center sm:space-x-4">
                        <div class="bg-violet-50 p-3 rounded-lg mb-3 sm:mb-0 lg:group-hover:opacity-0 transition-opacity duration-300">
                            <svg class="w-5 h-5 text-violet-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
                            </svg>
                        </div>
                        <div class="text-center sm:text-left">
                            <div class="flex flex-col sm:flex-row sm:items-baseline sm:gap-2">
                                <div class="text-3xl font-bold text-gray-800 mb-1 sm:mb-0">
                                    <span class="purecounter" data-purecounter-start="0" data-purecounter-end="10">0</span>+
                                </div>
                                <p class="text-base text-gray-500">Projects</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Experience -->
            <div class="relative">
                <div class="absolute inset-0 w-full h-full opacity-[0.5] lg:opacity-100 lg:w-11 lg:h-11 lg:left-4 lg:top-4 lg:inset-auto
                            group-hover:opacity-100 transition-all duration-300">
                    <lottie-player
                        src="https://lottie.host/9d9b8654-548f-4841-b4df-3f1aa59061c0/psuwcyw8ZS.json"
                        background="transparent"
                        speed="1"
                        loop
                        autoplay>
                    </lottie-player>
                </div>
                <div class="group bg-white p-4 rounded-xl hover:shadow-lg hover:bg-gray-50/80 transition-all w-64 sm:w-full relative z-10">
                    <div class="flex flex-col sm:flex-row items-center sm:space-x-4">
                        <div class="bg-blue-50 p-3 rounded-lg mb-3 sm:mb-0 group-hover:opacity-0 transition-opacity duration-300">
                            <svg class="w-5 h-5 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                            </svg>
                        </div>
                        <div class="text-center sm:text-left">
                            <div class="flex flex-col sm:flex-row sm:items-baseline sm:gap-2">
                                <div class="text-3xl font-bold text-gray-800 mb-1 sm:mb-0">
                                    <span class="purecounter" data-purecounter-start="0" data-purecounter-end="4">0</span>+
                                </div>
                                <p class="text-base text-gray-500">Years Active</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Clients -->
            <div class="relative">
                <div class="absolute inset-0 w-full h-full opacity-[0.5] lg:opacity-100 lg:w-11 lg:h-11 lg:left-4 lg:top-4 lg:inset-auto
                            group-hover:opacity-100 transition-all duration-300">
                    <lottie-player
                        src="https://lottie.host/ee68e9e4-2713-4a7a-9534-5604301e8380/TDmRHbSiwn.json"
                        background="transparent"
                        speed="1"
                        loop
                        autoplay>
                    </lottie-player>
                </div>
                <div class="group bg-white p-4 rounded-xl hover:shadow-lg hover:bg-gray-50/80 transition-all w-64 sm:w-full relative z-10">
                    <div class="flex flex-col sm:flex-row items-center sm:space-x-4">
                        <div class="bg-rose-50 p-3 rounded-lg mb-3 sm:mb-0 group-hover:opacity-0 transition-opacity duration-300">
                            <svg class="w-5 h-5 text-rose-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14.828 14.828a4 4 0 01-5.656 0M9 10h.01M15 10h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                            </svg>
                        </div>
                        <div class="text-center sm:text-left">
                            <div class="flex flex-col sm:flex-row sm:items-baseline sm:gap-2">
                                <div class="text-3xl font-bold text-gray-800 mb-1 sm:mb-0">
                                    <span class="purecounter" data-purecounter-start="0" data-purecounter-end="14">0</span>+
                                </div>
                                <p class="text-base text-gray-500">Clients</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Code -->
            <div class="relative">
                <div class="absolute inset-0 w-full h-full opacity-[0.5] lg:opacity-100 lg:w-11 lg:h-11 lg:left-4 lg:top-4 lg:inset-auto
                            group-hover:opacity-100 transition-all duration-300">
                    <lottie-player
                        src="https://lottie.host/c769ca5c-f1b0-4ff4-a791-9dbe9694470b/dP2CFe042x.json"
                        background="transparent"
                        speed="1"
                        loop
                        autoplay>
                    </lottie-player>
                </div>
                <div class="group bg-white p-4 rounded-xl hover:shadow-lg hover:bg-gray-50/80 transition-all w-64 sm:w-full relative z-10">
                    <div class="flex flex-col sm:flex-row items-center sm:space-x-4">
                        <div class="bg-emerald-50 p-3 rounded-lg mb-3 sm:mb-0 group-hover:opacity-0 transition-opacity duration-300">
                            <svg class="w-5 h-5 text-emerald-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 20l4-16m4 4l4 4-4 4M6 16l-4-4 4-4" />
                            </svg>
                        </div>
                        <div class="text-center sm:text-left">
                            <div class="flex flex-col sm:flex-row sm:items-baseline sm:gap-2">
                                <div class="text-3xl font-bold text-gray-800 mb-1 sm:mb-0">
                                    <span class="purecounter" data-purecounter-start="0" data-purecounter-end="32">0</span>k+
                                </div>
                                <p class="text-base text-gray-500">Code Lines</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

{{-- services section --}}
@include('front.partials.services')

{{-- hire form section --}}
@include('front.partials.hire-form')

{{-- education & experience section --}}
@include('front.partials.education-experience')

{{-- projects section --}}
@include('front.partials.projects')
{{-- end projects section --}}

{{-- certifications section --}}
@include('front.partials.certifications')
{{-- end certifications section --}}
{{-- skills section --}}
@include('front.partials.skills')
{{-- end skills section --}}
@endsection
@push('styles')
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/@splidejs/splide@4.1.4/dist/css/splide.min.css">
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/sweetalert2@11/dist/sweetalert2.min.css">
<style>
    @keyframes fade-in {
        from {
            opacity: 0;
        }

        to {
            opacity: 1;
        }
    }

    @keyframes fade-in-up {
        from {
            opacity: 0;
            transform: translateY(20px);
        }

        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    @keyframes float {

        0%,
        100% {
            transform: translateY(0);
        }

        50% {
            transform: translateY(-10px);
        }
    }

    @keyframes blob {

        0%,
        100% {
            transform: translate(0, 0) scale(1);
        }

        50% {
            transform: translate(10px, -10px) scale(1.05);
        }
    }

    .animate-fade-in {
        animation: fade-in 1s ease-out forwards;
    }

    .animate-fade-in-up {
        animation: fade-in-up 1s ease-out forwards;
    }

    .animate-float {
        animation: float 6s ease-in-out infinite;
    }

    .animate-blob {
        animation: blob 10s infinite;
    }

    .animation-delay-2000 {
        animation-delay: 2s;
    }

    .tech-bubble {
        transition: all 0.3s ease;
    }

    .tech-bubble:hover {
        transform: scale(1.1);
        box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
    }

    .splide__arrow {
        display: none;
    }

    .splide__pagination {
        display: none;
    }
</style>
@endpush

@push('scripts')
<script src="https://cdn.jsdelivr.net/npm/@splidejs/splide@4.1.4/dist/js/splide.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/@splidejs/splide-extension-auto-scroll@0.5.3/dist/js/splide-extension-auto-scroll.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/@srexi/purecounterjs/dist/purecounter_vanilla.js"></script>
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
<script src="https://unpkg.com/@lottiefiles/lottie-player@latest/dist/lottie-player.js"></script>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Inisialisasi untuk Core Technologies slider
        const coreSkillsSlider = new Splide('#core-technologies-slider', {
            type: 'loop',
            drag: 'free',
            focus: 'center',
            perPage: 'auto',
            autoWidth: true,
            gap: '1rem',
            arrows: false,
            pagination: false,
            autoScroll: {
                speed: 1,
                pauseOnHover: true,
                autoStart: true,
                rewind: false,
            },
            breakpoints: {
                640: {
                    perPage: 'auto',
                },
                768: {
                    perPage: 'auto',
                },
                1024: {
                    perPage: 'auto',
                }
            }
        });

        // Mount dengan AutoScroll extension
        if (window.splide && window.splide.Extensions) {
            coreSkillsSlider.mount({
                AutoScroll: window.splide.Extensions.AutoScroll
            });
        } else {
            coreSkillsSlider.mount();
        }
    });

    new PureCounter();

    function scrollToServices() {
        const servicesSection = document.getElementById('services');

        if (servicesSection) {
            const offset = 50; // Sama seperti di scrollToHireForm
            const elementPosition = servicesSection.getBoundingClientRect().top;
            const offsetPosition = elementPosition + window.pageYOffset - offset;

            window.scrollTo({
                top: offsetPosition,
                behavior: 'smooth'
            });
        }
    }
</script>
<script>
    function scrollToSection(sectionId, event) {
        event.preventDefault();

        if (sectionId === 'top') {
            window.scrollTo({
                top: 0,
                behavior: 'smooth'
            });
            return;
        }

        const section = document.getElementById(sectionId);

        if (section) {
            const offset = 50; // Menggunakan offset yang sama seperti scrollToServices
            const elementPosition = section.getBoundingClientRect().top;
            const offsetPosition = elementPosition + window.pageYOffset - offset;

            window.scrollTo({
                top: offsetPosition,
                behavior: 'smooth'
            });
        }
    }
</script>
@endpush
