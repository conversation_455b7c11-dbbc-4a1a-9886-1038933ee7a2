<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">
    <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <meta name="csrf-token" content="{{ csrf_token() }}">
        <meta name="description" content="Personal Portfolio Website">
        <meta name="keywords" content="@yield('meta_keywords', 'portfolio, services, professional')">

        <title>{{ config('app.name', 'Portfolio') }}</title>

        <!-- Favicon -->
        <link rel="icon" href="{{ asset('favicon.ico') }}" type="image/x-icon"/>

        <!-- Core CSS -->
        @vite(['resources/css/app.css', 'resources/js/app.js'])

        <!-- Third Party CSS -->
        <link rel="preconnect" href="https://fonts.bunny.net">
        <link href="https://fonts.bunny.net/css?family=figtree:400,500,600&display=swap" rel="stylesheet" />
        <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">
        <link href="https://unpkg.com/aos@2.3.1/dist/aos.css" rel="stylesheet">

        <!-- Third Party JS -->
        <script src="https://unpkg.com/alpinejs@3.x.x/dist/cdn.min.js" defer></script>
        <script src="https://unpkg.com/aos@2.3.1/dist/aos.js"></script>

        @livewireStyles

        <!-- Base Styles -->
        <style>
            [x-cloak] { display: none !important; }

            .primary-gradient {
                @apply bg-gradient-to-r from-primary-500 to-primary-600;
            }

            .dark-gradient {
                @apply bg-gradient-to-b from-gray-900 to-gray-800;
            }
        </style>

        <!-- Page Specific Styles -->
        @stack('styles')
        @yield('styles')
    </head>
    <body class="font-sans antialiased bg-white text-gray-800">
        <div class="min-h-screen flex flex-col">
            <!-- Header -->
            <header class="fixed w-full top-0 z-50 bg-white/80 backdrop-blur-sm border-b border-gray-200">
                @include('layouts.partials.frontend.navigation')
            </header>

            <!-- Main Content -->
            <main class="flex-grow pt-10">
                @yield('content')
            </main>

            <!-- Footer -->
            <footer class="bg-gray-100">
                @include('layouts.partials.frontend.footer')
            </footer>
        </div>

        <!-- Core Scripts -->
        @livewireScripts

        <!-- Initialize Third Party Libraries -->
        <script>
            // Initialize AOS
            document.addEventListener('DOMContentLoaded', function() {
                AOS.init({
                    duration: 800,
                    once: true,
                });
            });

            // Initialize Alpine.js
            document.addEventListener('alpine:init', () => {
                Alpine.data('navigation', () => ({
                    mobileMenuOpen: false
                }))
            });
        </script>

        <!-- Page Specific Scripts -->
        @stack('scripts')
        @yield('scripts')
    </body>
</html>
