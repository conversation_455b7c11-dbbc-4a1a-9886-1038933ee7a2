Stack trace:
Frame         Function      Args
0007FFFFBE20  00021005FE8E (000210285F68, 00021026AB6E, 000000000000, 0007FFFFAD20) msys-2.0.dll+0x1FE8E
0007FFFFBE20  0002100467F9 (000000000000, 000000000000, 000000000000, 0007FFFFC0F8) msys-2.0.dll+0x67F9
0007FFFFBE20  000210046832 (000210286019, 0007FFFFBCD8, 000000000000, 000000000000) msys-2.0.dll+0x6832
0007FFFFBE20  000210068CF6 (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28CF6
0007FFFFBE20  000210068E24 (0007FFFFBE30, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28E24
0007FFFFC100  00021006A225 (0007FFFFBE30, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A225
End of stack trace
Loaded modules:
000100400000 bash.exe
7FF81C9A0000 ntdll.dll
7FF81B830000 KERNEL32.DLL
7FF81A060000 KERNELBASE.dll
7FF8164A0000 apphelp.dll
7FF81A740000 USER32.dll
7FF819AF0000 win32u.dll
7FF81B020000 GDI32.dll
7FF819F20000 gdi32full.dll
7FF81A450000 msvcp_win.dll
7FF819DD0000 ucrtbase.dll
000210040000 msys-2.0.dll
7FF81BAF0000 advapi32.dll
7FF81BBB0000 msvcrt.dll
7FF81C050000 sechost.dll
7FF81BEF0000 RPCRT4.dll
7FF819000000 CRYPTBASE.DLL
7FF819D30000 bcryptPrimitives.dll
7FF81C010000 IMM32.DLL
