<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Spatie\Permission\Models\Role;
use Spatie\Permission\Models\Permission;
use App\Models\User;

class RolePermissionSeeder extends Seeder
{
    public function run(): void
    {
        // Reset cached roles and permissions
        app()[\Spatie\Permission\PermissionRegistrar::class]->forgetCachedPermissions();

        // Create Permissions
        $permissions = [
            // Profile Management
            'edit_profile',
            'view_profile',

            // Service Management
            'manage_services',

            // Project Management
            'manage_projects',

            // Experience Management
            'manage_experiences',

            // Education Management
            'manage_education',

            // Skill Management
            'manage_skills',

            // Hire Request Management
            'manage_hire_requests',

            // Message Management
            'manage_messages',

            // Setting Management
            'manage_settings',
        ];

        foreach ($permissions as $permission) {
            Permission::create(['name' => $permission]);
        }

        // Create Owner Role
        $ownerRole = Role::create(['name' => 'owner']);
        $ownerRole->givePermissionTo(Permission::all());

        // Create Owner User
        $owner = User::create([
            'name' => 'Admin',
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
            'email_verified_at' => now(),
        ]);
        $owner->assignRole('owner');
    }
}