<form wire:submit="submit" class="space-y-6">
    <div>
        <input type="text"
            wire:model="name"
            placeholder="Your Name"
            class="w-full px-4 py-3 bg-gray-800 border border-gray-700 rounded-lg text-white focus:outline-none focus:border-primary-500 @error('name') border-red-500 @enderror">
        @error('name')
            <span class="text-red-500 text-sm">{{ $message }}</span>
        @enderror
    </div>

    <div>
        <input type="email"
            wire:model="email"
            placeholder="Your Email"
            class="w-full px-4 py-3 bg-gray-800 border border-gray-700 rounded-lg text-white focus:outline-none focus:border-primary-500 @error('email') border-red-500 @enderror">
        @error('email')
            <span class="text-red-500 text-sm">{{ $message }}</span>
        @enderror
    </div>

    <div>
        <textarea
            wire:model="message"
            rows="4"
            placeholder="Your Message"
            class="w-full px-4 py-3 bg-gray-800 border border-gray-700 rounded-lg text-white focus:outline-none focus:border-primary-500 @error('message') border-red-500 @enderror"></textarea>
        @error('message')
            <span class="text-red-500 text-sm">{{ $message }}</span>
        @enderror
    </div>

    <button type="submit"
        class="w-full bg-primary-500 hover:bg-primary-600 text-white py-3 rounded-lg transition duration-300">
        Send Message
    </button>
</form>
