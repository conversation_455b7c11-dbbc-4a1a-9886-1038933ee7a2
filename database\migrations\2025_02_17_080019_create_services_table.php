<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('services', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->cascadeOnDelete();
            $table->string('name');
            $table->string('image')->nullable();
            $table->text('description');
            $table->decimal('price_range', 12, 0); // Untuk harga dalam Rupiah
            $table->enum('pricing_type', ['fixed', 'hourly', 'project'])->default('project');
            $table->enum('delivery_time', [
                '1-3 days',
                '3-7 days',
                '1-2 weeks',
                '2-4 weeks',
                '1-2 months',
                'custom'
            ]);
            $table->string('category');
            $table->boolean('is_active')->default(true);
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('services');
    }
};