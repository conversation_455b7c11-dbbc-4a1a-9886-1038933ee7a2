<?php

namespace App\Filament\Resources;

use App\Filament\Resources\SkillResource\Pages;
use App\Models\Skill;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class SkillResource extends Resource
{
    protected static ?string $model = Skill::class;
    protected static ?string $navigationIcon = 'heroicon-o-academic-cap';
    protected static ?string $navigationGroup = 'Portfolio';
    protected static ?int $navigationSort = 2;

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Hidden::make('user_id')
                    ->default(auth()->id()),

                Forms\Components\Section::make('Skill Information')
                    ->description('Configure the basic skill information.')
                    ->schema([
                        Forms\Components\TextInput::make('name')
                            ->required()
                            ->maxLength(255),
                        Forms\Components\FileUpload::make('img')
                            ->label('Skill Icon')
                            ->image()
                            ->directory('skill-icons')
                            ->columnSpanFull(),
                        Forms\Components\Select::make('type')
                            ->options([
                                'technology' => 'Technology',
                                'language' => 'Programming Language',
                                'framework' => 'Framework',
                                'tool' => 'Development Tool',
                                'soft_skill' => 'Soft Skill',
                            ])
                            ->required()
                            ->default('technology')
                            ->native(false),
                        Forms\Components\TextInput::make('proficiency')
                            ->label('Skill Level (%)')
                            ->numeric()
                            ->minValue(0)
                            ->maxValue(100)
                            ->step(5)
                            ->default(50)
                            ->suffix('%'),
                    ])->columns(2),

                Forms\Components\Section::make('Skill Categories')
                    ->description('Define how this skill should be displayed across your portfolio.')
                    ->schema([
                        Forms\Components\Grid::make(3)
                            ->schema([
                                Forms\Components\Toggle::make('is_core_technology')
                                    ->label('Core Technology')
                                    ->helperText('Show in profile as main technology')
                                    ->default(false),
                                Forms\Components\Toggle::make('is_highlighted')
                                    ->label('Highlight Skill')
                                    ->helperText('Feature this skill prominently')
                                    ->default(false),
                                Forms\Components\Toggle::make('is_project')
                                    ->label('Project Technology')
                                    ->helperText('Available for project tech stack')
                                    ->default(false),
                            ]),
                    ]),

                Forms\Components\Section::make('Display Settings')
                    ->schema([
                        Forms\Components\TextInput::make('order')
                            ->numeric()
                            ->default(0)
                            ->helperText('Set the display order (lower numbers appear first)'),
                    ]),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\ImageColumn::make('img')
                    ->label('Icon')
                    ->circular(),
                Tables\Columns\TextColumn::make('name')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('type')
                    ->badge()
                    ->color(fn (string $state): string => match ($state) {
                        'technology' => 'primary',
                        'framework' => 'success',
                        'language' => 'warning',
                        'tool' => 'info',
                        'soft_skill' => 'gray',
                        default => 'gray',
                    }),
                Tables\Columns\TextColumn::make('proficiency')
                    ->label('Level')
                    ->formatStateUsing(fn (int $state): string => "{$state}%"),
                Tables\Columns\IconColumn::make('is_core_technology')
                    ->label('Core')
                    ->boolean()
                    ->tooltip('Core Technology'),
                Tables\Columns\IconColumn::make('is_highlighted')
                    ->label('Featured')
                    ->boolean()
                    ->tooltip('Highlighted Skill'),
                Tables\Columns\IconColumn::make('is_project')
                    ->label('Project')
                    ->boolean()
                    ->tooltip('Project Technology'),
                Tables\Columns\TextColumn::make('order')
                    ->sortable(),
            ])
            ->defaultSort('order')
            ->filters([
                Tables\Filters\SelectFilter::make('type')
                    ->options([
                        'technology' => 'Technology',
                        'language' => 'Programming Language',
                        'framework' => 'Framework',
                        'tool' => 'Development Tool',
                        'soft_skill' => 'Soft Skill',
                    ]),
                Tables\Filters\TernaryFilter::make('is_core_technology')
                    ->label('Core Technology'),
                Tables\Filters\TernaryFilter::make('is_highlighted')
                    ->label('Highlighted'),
                Tables\Filters\TernaryFilter::make('is_project')
                    ->label('Project Technology'),
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListSkills::route('/'),
            'create' => Pages\CreateSkill::route('/create'),
            'edit' => Pages\EditSkill::route('/{record}/edit'),
        ];
    }

    public static function getEloquentQuery(): Builder
    {
        return parent::getEloquentQuery()
            ->where('user_id', auth()->id());
    }
}
