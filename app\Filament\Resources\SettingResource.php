<?php

namespace App\Filament\Resources;

use App\Filament\Resources\SettingResource\Pages;
use App\Models\Setting;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;

class SettingResource extends Resource
{
    protected static ?string $model = Setting::class;
    protected static ?string $navigationIcon = 'heroicon-o-cog-6-tooth';
    protected static ?string $navigationGroup = 'System';
    protected static ?int $navigationSort = 10;

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Hidden::make('user_id')
                    ->default(auth()->id()),
                Forms\Components\Select::make('key')
                    ->options([
                        'app_name' => 'Application Name',
                        'app_logo' => 'Application Logo',
                        'footer_about' => 'Footer About Text',
                    ])
                    ->required()
                    ->live()
                    ->unique(ignoreRecord: true),

                Forms\Components\Group::make()
                    ->schema([
                        Forms\Components\TextInput::make('value')
                            ->required()
                            ->visible(fn (Forms\Get $get) => $get('key') === 'app_name'),

                        Forms\Components\FileUpload::make('value')
                            ->image()
                            ->imageEditor()
                            ->directory('logo')
                            ->visibility('public')
                            ->visible(fn (Forms\Get $get) => $get('key') === 'app_logo')
                            ->columnSpanFull(),

                        Forms\Components\RichEditor::make('value')
                            ->visible(fn (Forms\Get $get) => $get('key') === 'footer_about')
                            ->columnSpanFull(),
                    ])
                    ->columnSpanFull(),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('key')
                    ->badge()
                    ->formatStateUsing(fn (string $state): string => match ($state) {
                        'app_name' => 'Application Name',
                        'app_logo' => 'Application Logo',
                        'footer_about' => 'Footer About Text',
                        default => $state,
                    }),
                Tables\Columns\TextColumn::make('value')
                    ->formatStateUsing(function ($record) {
                        return match ($record->key) {
                            'app_logo' => 'Image File',
                            'footer_about' => 'HTML Content',
                            default => $record->value,
                        };
                    }),
                Tables\Columns\TextColumn::make('updated_at')
                    ->dateTime()
                    ->sortable(),
            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListSiteSettings::route('/'),
            'create' => Pages\CreateSiteSetting::route('/create'),
            'edit' => Pages\EditSiteSetting::route('/{record}/edit'),
        ];
    }

    public static function getEloquentQuery(): Builder
    {
        return parent::getEloquentQuery()
            ->where('user_id', auth()->id());
    }
}
