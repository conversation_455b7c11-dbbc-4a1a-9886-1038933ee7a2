<?php

namespace App\Filament\Resources;

use App\Filament\Resources\ProjectResource\Pages;
use App\Models\Project;
use App\Models\Skill;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use Filament\Forms\Components\Select;
use Illuminate\Support\Facades\Auth;

class ProjectResource extends Resource
{
    protected static ?string $model = Project::class;
    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';
    protected static ?string $navigationGroup = 'Portfolio';
    protected static ?int $navigationSort = 2;

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('Basic Information')
                    ->schema([
                        Forms\Components\Select::make('user_id')
                            ->relationship('user', 'name')
                            ->required()
                            ->hiddenOn('edit'),
                        Forms\Components\TextInput::make('title')
                            ->required()
                            ->maxLength(255)
                            ->placeholder('Enter project title'),
                        Forms\Components\Select::make('type')
                            ->options([
                                'personal' => 'Personal Project',
                                'client' => 'Client Project',
                                'open-source' => 'Open Source',
                            ])
                            ->required()
                            ->native(false),
                        Forms\Components\TextInput::make('client')
                            ->maxLength(255)
                            ->placeholder('Client name or company')
                            ->visible(fn (Forms\Get $get) => $get('type') === 'client'),
                    ])->columns(2),

                Forms\Components\Section::make('Project Details')
                    ->schema([
                        Forms\Components\RichEditor::make('description')
                            ->required()
                            ->toolbarButtons([
                                'bold',
                                'italic',
                                'link',
                                'bulletList',
                                'orderedList',
                            ])
                            ->columnSpanFull(),

                        Select::make('skills')
                            ->relationship(
                                name: 'skills',
                                titleAttribute: 'name',
                                modifyQueryUsing: fn ($query) => $query
                                    ->where('is_project', true)
                                    ->orderBy('skills.order')
                            )
                            ->multiple()
                            ->preload()
                            ->searchable()
                            ->columnSpanFull()
                            ->createOptionForm([
                                Forms\Components\Hidden::make('user_id')
                                    ->default(auth()->id()),

                                Forms\Components\Section::make('Skill Information')
                                    ->description('Configure the basic skill information.')
                                    ->schema([
                                        Forms\Components\TextInput::make('name')
                                            ->required()
                                            ->maxLength(255),
                                        Forms\Components\FileUpload::make('img')
                                            ->label('Skill Icon')
                                            ->image()
                                            ->directory('skill-icons')
                                            ->columnSpanFull(),
                                        Forms\Components\Select::make('type')
                                            ->options([
                                                'technology' => 'Technology',
                                                'language' => 'Programming Language',
                                                'framework' => 'Framework',
                                                'tool' => 'Development Tool',
                                                'soft_skill' => 'Soft Skill',
                                            ])
                                            ->required()
                                            ->default('technology')
                                            ->native(false),
                                        Forms\Components\TextInput::make('proficiency')
                                            ->label('Skill Level (%)')
                                            ->numeric()
                                            ->minValue(0)
                                            ->maxValue(100)
                                            ->step(5)
                                            ->default(50)
                                            ->suffix('%'),
                                    ])->columns(2),

                                Forms\Components\Hidden::make('is_project')
                                    ->default(true),
                                Forms\Components\Hidden::make('is_core_technology')
                                    ->default(false),
                                Forms\Components\Hidden::make('is_highlighted')
                                    ->default(false),

                                Forms\Components\Hidden::make('order')
                                    ->default(fn () => \App\Models\Skill::where('user_id', auth()->id())->max('order') + 1),
                            ])
                            ->columns(2),

                        Forms\Components\Repeater::make('features')
                            ->schema([
                                Forms\Components\TextInput::make('feature')
                                    ->required()
                                    ->maxLength(255)
                                    ->placeholder('Enter project feature')
                                    ->columnSpanFull(),
                            ])
                            ->columnSpanFull()
                            ->defaultItems(1)
                            ->reorderable()
                            ->collapsible()
                            ->itemLabel(fn (array $state): ?string => $state['feature'] ?? null)
                            ->addActionLabel('Add Feature'),
                    ]),

                Forms\Components\Section::make('Project Media')
                    ->schema([
                        Forms\Components\FileUpload::make('image_url')
                            ->image()
                            ->required()
                            ->imageEditor()
                            ->columnSpanFull()
                            ->directory('projects')
                            ->visibility('public')
                            ->imageResizeMode('cover')
                            ->imageCropAspectRatio('16:9'),
                        Forms\Components\Repeater::make('gallery')
                            ->schema([
                                Forms\Components\FileUpload::make('image')
                                    ->image()
                                    ->required()
                                    ->directory('projects/gallery')
                                    ->visibility('public'),
                                Forms\Components\TextInput::make('caption')
                                    ->maxLength(255),
                            ])
                            ->columnSpanFull()
                            ->defaultItems(0)
                            ->reorderable()
                            ->collapsible(),
                    ]),

                Forms\Components\Section::make('Project Timeline')
                    ->schema([
                        Forms\Components\DatePicker::make('start_date')
                            ->required(),
                        Forms\Components\DatePicker::make('end_date')
                            ->after('start_date'),
                        Forms\Components\TextInput::make('preview_url')
                            ->url()
                            ->prefix('https://')
                            ->placeholder('project-demo.com'),
                    ])->columns(2),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\ImageColumn::make('image_url')
                    ->square(),
                Tables\Columns\TextColumn::make('title')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\BadgeColumn::make('type')
                    ->colors([
                        'primary' => 'personal',
                        'success' => 'client',
                        'warning' => 'open-source',
                    ]),
                Tables\Columns\TextColumn::make('project_period')
                    ->label('Duration'),
                Tables\Columns\TextColumn::make('skills.name')
                    ->badge()
                    ->separator(',')
                    ->limitList(3),
                Tables\Columns\TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                Tables\Filters\TrashedFilter::make(),
                Tables\Filters\SelectFilter::make('type')
                    ->options([
                        'personal' => 'Personal',
                        'client' => 'Client',
                        'open-source' => 'Open Source',
                    ]),
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                    Tables\Actions\ForceDeleteBulkAction::make(),
                    Tables\Actions\RestoreBulkAction::make(),
                ]),
            ]);
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListProjects::route('/'),
            'create' => Pages\CreateProject::route('/create'),
            'edit' => Pages\EditProject::route('/{record}/edit'),
        ];
    }

    public static function getEloquentQuery(): Builder
    {
        return parent::getEloquentQuery()
            ->withoutGlobalScopes([
                SoftDeletingScope::class,
            ]);
    }
}