<nav x-data="{ mobileMenuOpen: false }"
     x-init="
        $watch('mobileMenuOpen', value => {
            if (value) {
                document.body.classList.add('overflow-hidden');
            } else {
                document.body.classList.remove('overflow-hidden');
            }
        })
     "
     class="fixed top-0 left-0 right-0 z-50 bg-white/80 border-b border-gray-200">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex justify-between items-center h-16">
            <!-- Logo -->
            <div class="flex-shrink-0 flex items-center">
                <a href="<?php echo e(route('home')); ?>" class="flex items-center">
                    <?php
                        $settings = \App\Models\Setting::where('user_id', $profile->user_id)->get()->pluck('value', 'key');
                    ?>

                    <?php if(isset($settings['app_logo'])): ?>
                        <img src="<?php echo e(Storage::url($settings['app_logo'])); ?>"
                             alt="<?php echo e($settings['app_name'] ?? config('app.name')); ?>"
                             class="h-8 w-auto"
                        >
                    <?php else: ?>
                        <img src="<?php echo e(asset('images/logo.png')); ?>"
                             alt="<?php echo e($settings['app_name'] ?? config('app.name')); ?>"
                             class="h-8 w-auto"
                        >
                    <?php endif; ?>
                    <span class="ml-2 text-xl font-bold text-gray-900">
                        <?php echo e($settings['app_name'] ?? config('app.name')); ?>

                    </span>
                </a>
            </div>

            <!-- Desktop Menu -->
            <div class="hidden md:flex items-center space-x-8">
                <a href="<?php echo e(route('home')); ?>"
                   class="text-gray-600 hover:text-primary-600 px-3 py-2 text-sm font-medium transition-colors <?php echo e(request()->routeIs('home') ? 'text-primary-600' : ''); ?>">
                    Home
                </a>
                <a href="#services" onclick="scrollToSection('services', event)"
                   class="text-gray-600 hover:text-primary-600 px-3 py-2 text-sm font-medium transition-colors">
                    Services
                </a>
                <a href="#projects" onclick="scrollToSection('projects', event)"
                   class="text-gray-600 hover:text-primary-600 px-3 py-2 text-sm font-medium transition-colors">
                    Projects
                </a>
                <a href="#certifications" onclick="scrollToSection('certifications', event)"
                   class="text-gray-600 hover:text-primary-600 px-3 py-2 text-sm font-medium transition-colors">
                    Certifications
                </a>
                <a href="#hire" onclick="scrollToSection('hire', event)"
                   class="text-gray-600 hover:text-primary-600 px-3 py-2 text-sm font-medium transition-colors">
                    Contact
                </a>
            </div>

            <!-- Auth Menu -->
            <div class="hidden md:flex items-center space-x-4">
                <?php if(auth()->guard()->check()): ?>
                    <a href="<?php echo e(route('filament.admin.pages.dashboard')); ?>"
                       class="text-gray-600 hover:text-primary-600 px-3 py-2 text-sm font-medium transition-colors">
                        Dashboard
                    </a>
                    <form method="POST" action="<?php echo e(route('logout')); ?>">
                        <?php echo csrf_field(); ?>
                        <button type="submit"
                                class="text-gray-600 hover:text-primary-600 px-3 py-2 text-sm font-medium transition-colors">
                            Logout
                        </button>
                    </form>
                <?php else: ?>
                    <a href="<?php echo e(route('filament.admin.auth.login')); ?>"
                       class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-primary-600 bg-primary-50 hover:bg-primary-100 transition-colors">
                        Login
                    </a>
                <?php endif; ?>
            </div>

            <!-- Mobile menu button -->
            <div class="md:hidden">
                <button @click="mobileMenuOpen = !mobileMenuOpen"
                        class="inline-flex items-center justify-center p-2 rounded-md text-gray-600 hover:text-primary-600 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-primary-500 transition-colors">
                    <span class="sr-only">Open main menu</span>
                    <!-- Icon when menu is closed -->
                    <svg x-show="!mobileMenuOpen" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16" />
                    </svg>
                    <!-- Icon when menu is open -->
                    <svg x-show="mobileMenuOpen" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                    </svg>
                </button>
            </div>
        </div>
    </div>

    <!-- Mobile menu -->
    <div x-cloak
         x-show="mobileMenuOpen"
         x-transition:enter="transition ease-out duration-200"
         x-transition:enter-start="opacity-0 transform -translate-y-4"
         x-transition:enter-end="opacity-100 transform translate-y-0"
         x-transition:leave="transition ease-in duration-150"
         x-transition:leave-start="opacity-100 transform translate-y-0"
         x-transition:leave-end="opacity-0 transform -translate-y-4"
         @click.away="mobileMenuOpen = false"
         class="md:hidden bg-white border-b border-gray-200">
        <div class="px-2 pt-2 pb-3 space-y-1">
            <a href="<?php echo e(route('home')); ?>"
               class="block px-3 py-2 rounded-md text-base font-medium text-gray-600 hover:text-primary-600 hover:bg-gray-50 transition-colors <?php echo e(request()->routeIs('home') ? 'text-primary-600 bg-primary-50' : ''); ?>">
                Home
            </a>
            <a href="#services" onclick="scrollToSection('services', event)"
               class="block px-3 py-2 rounded-md text-base font-medium text-gray-600 hover:text-primary-600 hover:bg-gray-50 transition-colors <?php echo e(request()->routeIs('about') ? 'text-primary-600 bg-primary-50' : ''); ?>">
                Services
            </a>
            <a href="#projects" onclick="scrollToSection('projects', event)"
               class="block px-3 py-2 rounded-md text-base font-medium text-gray-600 hover:text-primary-600 hover:bg-gray-50 transition-colors <?php echo e(request()->routeIs('services') ? 'text-primary-600 bg-primary-50' : ''); ?>">
                Projects
            </a>
            <a href="#certifications" onclick="scrollToSection('certifications', event)"
               class="block px-3 py-2 rounded-md text-base font-medium text-gray-600 hover:text-primary-600 hover:bg-gray-50 transition-colors <?php echo e(request()->routeIs('portfolio') ? 'text-primary-600 bg-primary-50' : ''); ?>">
                Certifications
            </a>
            <a href="#hire" onclick="scrollToSection('hire', event)"
               class="block px-3 py-2 rounded-md text-base font-medium text-gray-600 hover:text-primary-600 hover:bg-gray-50 transition-colors <?php echo e(request()->routeIs('contact') ? 'text-primary-600 bg-primary-50' : ''); ?>">
                Contact
            </a>

            <!-- Mobile Auth Menu -->
            <div class="border-t border-gray-200 pt-4 pb-3">
                <?php if(auth()->guard()->check()): ?>
                    <a href="<?php echo e(route('filament.admin.pages.dashboard')); ?>"
                       class="block px-3 py-2 rounded-md text-base font-medium text-gray-600 hover:text-primary-600 hover:bg-gray-50 transition-colors">
                        Dashboard
                    </a>
                    <form method="POST" action="<?php echo e(route('logout')); ?>">
                        <?php echo csrf_field(); ?>
                        <button type="submit"
                                class="block w-full text-left px-3 py-2 rounded-md text-base font-medium text-gray-600 hover:text-primary-600 hover:bg-gray-50 transition-colors">
                            Logout
                        </button>
                    </form>
                <?php else: ?>
                    <a href="<?php echo e(route('filament.admin.auth.login')); ?>"
                       class="block px-3 py-2 rounded-md text-base font-medium text-gray-600 hover:text-primary-600 hover:bg-gray-50 transition-colors">
                        Login
                    </a>
                <?php endif; ?>
            </div>
        </div>
    </div>
</nav>

<?php $__env->startPush('styles'); ?>
<style>
    [x-cloak] {
        display: none !important;
    }
</style>
<?php $__env->stopPush(); ?>
<?php /**PATH D:\Project Code\portfolio\resources\views/layouts/partials/frontend/navigation.blade.php ENDPATH**/ ?>