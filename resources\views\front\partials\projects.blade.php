<section id="projects" class="lg:py-10 py-5 relative overflow-hidden">
    <!-- Background Elements -->
    <div class="absolute inset-0" style="z-index: -1;">
        <div class="absolute inset-0 bg-gradient-to-br from-gray-50 to-white"></div>
        <div class="absolute inset-0 bg-[linear-gradient(to_right,#80808012_1px,transparent_1px),linear-gradient(to_bottom,#80808012_1px,transparent_1px)] bg-[size:24px_24px]"></div>
    </div>

    <div class="max-w-[1200px] mx-auto px-4">
        <!-- Section Header -->
        <div class="text-center mb-4">
            <div class="inline-flex items-center gap-2 px-4 py-2 rounded-full bg-white/80 backdrop-blur shadow-lg mb-4">
                <span class="relative flex h-2 w-2">
                    <span class="animate-ping absolute inline-flex h-full w-full rounded-full bg-blue-400 opacity-75"></span>
                    <span class="relative inline-flex rounded-full h-2 w-2 bg-blue-500"></span>
                </span>
                <span class="text-sm font-medium bg-gradient-to-r from-blue-600 to-sky-600 bg-clip-text text-transparent">
                    Featured Work
                </span>
            </div>
            <h2 class="lg:text-3xl text-2xl font-bold text-gray-900 mb-2">Recent Projects</h2>
            <p class="text-gray-600 max-w-2xl mx-auto">
                Explore some of my latest projects and creative solutions
            </p>
        </div>

        @php
            $projects = $profile->user->projects()->latest()->get();
        @endphp

        @if($projects->isNotEmpty())
            <div class="splide" id="projects-slider">
                <div class="splide__track">
                    <div class="splide__list">
                        @foreach($projects as $project)
                        <article class="splide__slide p-4" data-aos="fade-up" data-aos-delay="{{ $loop->index * 100 }}">
                            <div class="bg-white rounded-2xl shadow-md hover:shadow-xl transition-all duration-300 overflow-hidden h-full flex flex-col">
                                <!-- Media Section -->
                                <div class="relative">
                                    <!-- Project Type & Status Badges -->
                                    <div class="absolute top-4 left-4 right-4 flex justify-between z-10">
                                        <div class="flex flex-wrap gap-2">
                                            <span class="px-3 py-1.5 text-xs font-medium rounded-full shadow-sm bg-blue-100/90 text-blue-600">
                                                {{ ucfirst($project->type) }}
                                            </span>
                                            <span @class([
                                                'px-3 py-1.5 text-xs font-medium rounded-full shadow-sm',
                                                'bg-rose-100/90 text-rose-700' => !$project->end_date,
                                                'bg-teal-100/90 text-teal-700' => $project->end_date,
                                            ])>
                                                {{ $project->end_date ? 'Completed' : 'Ongoing' }}
                                            </span>
                                        </div>
                                    </div>

                                    <!-- Cover Image with Gallery Trigger -->
                                    <div class="aspect-video relative cursor-pointer group"
                                         onclick="openGallery('gallery-{{ $project->id }}')">
                                        <img src="{{ Storage::url($project->image_url) }}"
                                             alt="{{ $project->title }}"
                                             class="w-full h-full object-cover">
                                        @if($project->gallery && count($project->gallery) > 0)
                                        <div class="absolute inset-0 bg-black/60 opacity-0 group-hover:opacity-100 transition-opacity flex items-center justify-center">
                                            <span class="text-white flex items-center gap-2">
                                                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2V6zM14 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V6zM4 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2v-2zM14 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2v-2z"/>
                                                </svg>
                                                View Gallery ({{ count($project->gallery) }} images)
                                            </span>
                                        </div>
                                        @endif
                                    </div>
                                </div>

                                <!-- Content Section -->
                                <div class="p-6 flex-grow flex flex-col">
                                    <!-- Header with Timeline -->
                                    <div class="mb-2">
                                        <div class="flex items-center justify-between gap-4 mb-2">
                                            <h3 class="text-xl font-semibold text-gray-900">{{ $project->title }}</h3>
                                            <span class="px-3 py-1.5 text-xs font-medium rounded-full bg-indigo-50 text-blue-600 border border-indigo-100">
                                                {{ $project->project_period }}
                                            </span>
                                        </div>

                                        <div class="flex items-center gap-3 text-sm text-gray-600">
                                            <div class="flex items-center gap-1.5">
                                                <svg class="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"/>
                                                </svg>
                                                <span class="px-2 py-1 rounded-md bg-gray-50">
                                                    {{ $project->start_date->format('d M Y') }}
                                                </span>
                                                <span class="text-gray-400">→</span>
                                                <span class="px-2 py-1 rounded-md bg-gray-50">
                                                    {{ $project->end_date ? $project->end_date->format('d M Y') : 'Present' }}
                                                </span>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Description with Read More -->
                                    <div x-data="{ expanded: false }" class="relative mb-4">
                                        <div class=text-sm prose prose-sm max-w-none" :class="{ 'line-clamp-3': !expanded }">
                                            {!! $project->description !!}
                                        </div>
                                        <button @click="expanded = !expanded"
                                                class="mt-2 text-sm font-medium text-blue-600 hover:text-blue-700 focus:outline-none inline-flex items-center gap-1">
                                            <span x-text="expanded ? 'Show Less' : 'Read More'"></span>
                                            <svg class="w-4 h-4"
                                                 :class="{ 'rotate-180': expanded }"
                                                 fill="none"
                                                 stroke="currentColor"
                                                 viewBox="0 0 24 24">
                                                <path stroke-linecap="round"
                                                      stroke-linejoin="round"
                                                      stroke-width="2"
                                                      d="M19 9l-7 7-7-7"/>
                                            </svg>
                                        </button>
                                    </div>

                                    <!-- Features -->
                                    @if($project->features && count($project->features) > 0)
                                    <div class="mb-4">
                                        <h4 class="text-sm font-medium text-gray-900 mb-2">Key Features</h4>
                                        <ul class="space-y-1.5">
                                            @foreach($project->features as $feature)
                                            <li class="flex items-start gap-2 text-sm text-gray-600">
                                                <svg class="w-5 h-5 text-blue-500 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4"/>
                                                </svg>
                                                {{ $feature['feature'] }}
                                            </li>
                                            @endforeach
                                        </ul>
                                    </div>
                                    @endif

                                    <!-- Tech Stack -->
                                    @if($project->skills->isNotEmpty())
                                    <div class="mb-4">
                                        <h4 class="text-sm font-medium text-gray-900 mb-2">Technologies Used</h4>
                                        <div class="px-2">
                                            <div class="splide tech-stack-slider" id="tech-stack-{{ $project->id }}">
                                                <div class="splide__track">
                                                    <div class="splide__list">
                                                        @foreach($project->skills as $skill)
                                                        <div class="splide__slide">
                                                            <span class="inline-flex items-center gap-1.5 px-2.5 py-1.5 rounded-md bg-gray-100 text-xs font-medium text-gray-700 hover:bg-gray-200 transition-colors">
                                                                @if($skill->img)
                                                                <img src="{{ Storage::url($skill->img) }}"
                                                                     alt="{{ $skill->name }}"
                                                                     class="w-4 h-4">
                                                                @endif
                                                                {{ $skill->name }}
                                                            </span>
                                                        </div>
                                                        @endforeach
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    @endif

                                    <!-- Actions -->
                                    <div class="mt-auto pt-4 border-t border-gray-100 flex gap-4">
                                        @if($project->preview_url)
                                        <a href="{{ $project->preview_url }}"
                                           target="_blank"
                                           class="inline-flex items-center gap-2 text-sm font-medium text-blue-600 hover:text-blue-700 transition-colors">
                                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14"/>
                                            </svg>
                                            Live Preview
                                        </a>
                                        @endif

                                        @if($project->github_url)
                                        <a href="{{ $project->github_url }}"
                                           target="_blank"
                                           class="inline-flex items-center gap-2 text-sm font-medium text-gray-600 hover:text-gray-800 transition-colors">
                                            <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 24 24">
                                                <path d="M12 0c-6.626 0-12 5.373-12 12 0 5.302 3.438 9.8 8.207 11.387.599.111.793-.261.793-.577v-2.234c-3.338.726-4.033-1.416-4.033-1.416-.546-1.387-1.333-1.756-1.333-1.756-1.089-.745.083-.729.083-.729 1.205.084 1.839 1.237 1.237 1.237 1.07 1.834 2.807 1.304 3.492.997.107-.775.418-1.305.762-1.604-2.665-.305-5.467-1.334-5.467-5.931 0-1.311.469-2.381 1.236-3.221-.124-.303-.535-1.524.117-3.176 0 0 1.008-.322 3.301 1.23.957-.266 1.983-.399 3.003-.404 1.02.005 2.047.138 3.006.404 2.291-1.552 3.297-1.23 3.297-1.23.653 1.653.242 2.874.118 3.176.77.84 1.235 1.911 1.235 3.221 0 4.609-2.807 5.624-5.479 5.921.43.372.823 1.102.823 2.222v3.293c0 .319.192.694.801.576 4.765-1.589 8.199-6.086 8.199-11.386 0-6.627-5.373-12-12-12z"/>
                                            </svg>
                                            Source Code
                                        </a>
                                        @endif
                                    </div>
                                </div>
                            </div>
                        </article>
                        @endforeach
                    </div>
                </div>
            </div>

            <!-- Gallery Modal -->
            @foreach($projects as $project)
                @if($project->gallery && count($project->gallery) > 0)
                <div id="gallery-{{ $project->id }}" class="fixed inset-0 z-50 hidden">
                    <!-- Backdrop with click handler -->
                    <div class="fixed inset-0 bg-black/90 backdrop-blur-sm" onclick="closeGallery('gallery-{{ $project->id }}')"></div>

                    <!-- Gallery Container -->
                    <div class="relative z-10 h-full w-full flex items-center justify-center p-4">
                        <!-- Close Button -->
                        <button onclick="closeGallery('gallery-{{ $project->id }}')"
                                class="absolute top-[7rem] lg:top-4 right-4 p-2 text-white/80 hover:text-white focus:outline-none transition-colors z-50
                                       bg-black/20 hover:bg-black/40 rounded-full backdrop-blur-sm">
                            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
                            </svg>
                        </button>

                        <!-- Splide Gallery -->
                        <div class="w-full max-w-5xl" onclick="event.stopPropagation()">
                            <div class="splide gallery-slider" id="gallery-slider-{{ $project->id }}">
                                <div class="splide__track">
                                    <ul class="splide__list">
                                        @foreach($project->gallery as $image)
                                        <li class="splide__slide">
                                            <figure class="relative inline-block">
                                                <div class="relative">
                                                    <img src="{{ Storage::url($image['image']) }}"
                                                         alt="{{ $image['caption'] ?? $project->title }}"
                                                         class="max-h-[80vh] w-auto object-contain rounded-lg">

                                                    @if(isset($image['caption']))
                                                    <figcaption class="absolute bottom-0 inset-x-0 rounded-b-lg overflow-hidden">
                                                        <div class="p-3 bg-gradient-to-t from-black via-black/75 to-transparent"
                                                             style="background: linear-gradient(to top, rgba(0,0,0,0.9), rgba(0,0,0,0.75) 50%, transparent 100%);">
                                                            <p class="text-white text-center text-sm md:text-base">{{ $image['caption'] }}</p>
                                                        </div>
                                                    </figcaption>
                                                    @endif
                                                </div>
                                            </figure>
                                        </li>
                                        @endforeach
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                @endif
            @endforeach
        @else
            <div class="text-center py-12">
                <div class="bg-white rounded-lg shadow-sm p-8 max-w-2xl mx-auto">
                    <svg class="w-16 h-16 text-gray-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"/>
                    </svg>
                    <h3 class="text-lg font-medium text-gray-900 mb-2">No Projects Yet</h3>
                    <p class="text-gray-600">Projects will appear here once they are added to your portfolio.</p>
                </div>
            </div>
        @endif
    </div>
</section>

@push('styles')
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/@splidejs/splide@4.1.4/dist/css/splide.min.css">
<style>
    /* Prose styles */
    .prose img {
        margin: 0;
    }

    .prose {
        transition: all 0.3s ease-in-out;
    }

    .line-clamp-3 {
        display: -webkit-box;
        -webkit-line-clamp: 3;
        -webkit-box-orient: vertical;
        overflow: hidden;
    }

    /* Gallery styles */
    body.gallery-open {
        overflow: hidden;
    }

    /* Center image in gallery slide */
    .gallery-slider .splide__track,
    .gallery-slider .splide__list {
        height: 100%;
    }

    .gallery-slider .splide__slide {
        display: flex;
        align-items: center;
        justify-content: center;
        height: 80vh;
    }

    .gallery-slider .splide__slide figure {
        margin: 0;
        width: 100%;
        height: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
        position: relative;
    }

    .gallery-slider .splide__slide img {
        max-height: 80vh;
        max-width: 100%;
        width: auto;
        height: auto;
        object-fit: contain;
    }


     /* Pagination dots styling */
     .gallery-slider .splide__pagination {
        bottom: 50px;
        display: flex;
    }

    @media (min-width: 768px) {

        /* Pagination dots styling */
    .gallery-slider .splide__pagination {
        bottom: -2rem;
        display: flex;
    }
    }



    .gallery-slider .splide__pagination__page {
        width: 8px;
        height: 8px;
        margin: 0 4px;
        background: rgba(255, 255, 255, 0.3);
        transition: all 0.2s;
    }

    .gallery-slider .splide__pagination__page.is-active {
        background: rgba(255, 255, 255, 0.9);
        transform: scale(1.2);
    }

    /* Tech Stack Slider styles */
    .tech-stack-slider .splide__track {
        overflow: hidden;
    }

    .tech-stack-slider .splide__list {
        display: flex;
        align-items: center;
    }

    .tech-stack-slider .splide__slide {
        width: auto;
        flex-shrink: 0;
    }
</style>
@endpush

@push('scripts')
<script src="https://cdn.jsdelivr.net/npm/@splidejs/splide@4.1.4/dist/js/splide.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/@splidejs/splide-extension-auto-scroll@0.5.3/dist/js/splide-extension-auto-scroll.min.js"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Initialize main projects slider
    new Splide('#projects-slider', {
        perPage: 3,
        gap: '2rem',
        breakpoints: {
            1024: {
                perPage: 2,
            },
            640: {
                perPage: 1,
            }
        },
        pagination: true,
        arrows: true,
    }).mount();

    // Initialize all gallery sliders
    document.querySelectorAll('.gallery-slider').forEach(slider => {
        new Splide(slider, {
            type: 'fade',
            rewind: true,
            arrows: window.innerWidth >= 768, // Show arrows only on desktop
            pagination: true, // Always show pagination
            keyboard: true,
            drag: true,
            speed: 600,
            easing: 'cubic-bezier(0.4, 0, 0.2, 1)',
            classes: {
                arrows: 'splide__arrows gallery-arrows',
                arrow: 'splide__arrow gallery-arrow',
                prev: 'splide__arrow--prev gallery-arrow-prev',
                next: 'splide__arrow--next gallery-arrow-next',
            }
        }).mount();
    });

    // Initialize tech stack sliders
    document.querySelectorAll('.tech-stack-slider').forEach(slider => {
        new Splide(slider, {
            type: 'loop',
            perPage: 'auto',
            gap: '0.5rem',
            pagination: false,
            arrows: false,
            drag: true,
            padding: { left: '.5rem', right: '.5rem' },
            autoWidth: true,
            focus: 0,
            trimSpace: false,
            autoScroll: {
                speed: 1,
                pauseOnHover: true,
                pauseOnFocus: true,
            },
            breakpoints: {
                640: {
                    padding: { left: 0, right: '0.5rem' },
                }
            }
        }).mount({ AutoScroll: window.splide.Extensions.AutoScroll });
    });

    // Update controls on window resize
    window.addEventListener('resize', function() {
        document.querySelectorAll('.gallery-slider').forEach(slider => {
            const splide = Splide.get(slider);
            if (splide) {
                splide.options = {
                    arrows: window.innerWidth >= 768,
                    pagination: window.innerWidth < 768
                };
            }
        });
    });

    // Refresh AOS after Splide initialization
    setTimeout(function() {
        AOS.refresh();
    }, 500);
});

function openGallery(galleryId) {
    const gallery = document.getElementById(galleryId);
    gallery.classList.remove('hidden');
    document.body.classList.add('gallery-open');

    // Add click event to close gallery when clicking outside
    gallery.addEventListener('click', function(e) {
        // Close if clicking the dark overlay (not the image or navigation)
        if (!e.target.closest('.splide') && !e.target.closest('button')) {
            closeGallery(galleryId);
        }
    });

    // Add escape key handler
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape') {
            closeGallery(galleryId);
        }
    });
}

function closeGallery(galleryId) {
    const gallery = document.getElementById(galleryId);
    gallery.classList.add('hidden');
    document.body.classList.remove('gallery-open');
}
</script>
@endpush
