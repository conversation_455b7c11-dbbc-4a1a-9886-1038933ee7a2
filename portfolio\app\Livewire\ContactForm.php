<?php

namespace App\Livewire;

use App\Models\HireRequest;
use Livewire\Component;
use Filament\Notifications\Notification;

class ContactForm extends Component
{
    public $name = '';
    public $email = '';
    public $message = '';

    protected $rules = [
        'name' => 'required|min:3',
        'email' => 'required|email',
        'message' => 'required|min:10',
    ];

    public function submit()
    {
        $this->validate();

        HireRequest::create([
            'name' => $this->name,
            'email' => $this->email,
            'message' => $this->message,
        ]);

        $this->reset();

        Notification::make()
            ->title('Message sent successfully!')
            ->success()
            ->send();
    }

    public function render()
    {
        return view('livewire.contact-form');
    }
}
