<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use App\Models\Service;
use Illuminate\Support\Facades\Storage;

Route::get('/user', function (Request $request) {
    return $request->user();
})->middleware('auth:sanctum');

Route::get('/services/{service}', function (Service $service) {
    return response()->json([
        'id' => $service->id,
        'name' => $service->name,
        'description' => $service->description,
        'category' => $service->category,
        'delivery_time' => $service->delivery_time,
        'formatted_price' => $service->formatted_price,
        'image' => $service->image ? Storage::url($service->image) : null,
    ]);
});
