<section class="lg:py-10 py-5 relative overflow-hidden">
    <!-- Background Elements -->
    <div class="absolute inset-0" style="z-index: -1;">
        <div class="absolute inset-0 bg-gradient-to-br from-gray-50 to-white"></div>
        <div class="absolute inset-0 bg-[linear-gradient(to_right,#80808012_1px,transparent_1px),linear-gradient(to_bottom,#80808012_1px,transparent_1px)] bg-[size:24px_24px]"></div>
    </div>

    <div class="max-w-[1000px] mx-auto px-4">
        <!-- Section Header -->
        <div class="text-center mb-4">
            <div class="inline-flex items-center gap-2 px-4 py-2 rounded-full bg-white/80 backdrop-blur shadow-lg mb-4">
                <span class="relative flex h-2 w-2">
                    <span class="animate-ping absolute inline-flex h-full w-full rounded-full bg-rose-400 opacity-75"></span>
                    <span class="relative inline-flex rounded-full h-2 w-2 bg-rose-500"></span>
                </span>
                <span class="text-sm font-medium bg-gradient-to-r from-rose-500 to-rose-600 bg-clip-text text-transparent">
                    Technical Expertise
                </span>
            </div>
            <h2 class="lg:text-3xl text-2xl font-bold text-gray-900 mb-2">Professional Skills</h2>
            <p class="text-gray-600 max-w-2xl mx-auto">
                Technologies and tools I've mastered throughout my journey
            </p>
        </div>

        <!-- Skills List -->
        <?php if($skills->isNotEmpty()): ?>
            <div class="grid lg:grid-cols-2 gap-6">
                <?php $__currentLoopData = $skills->sortByDesc('proficiency'); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $skill): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <div class="skill-item" data-aos="fade-up" data-aos-delay="<?php echo e($loop->index * 50); ?>">
                    <div class="flex items-center justify-between mb-2">
                        <div class="flex items-center gap-2">
                            <?php if($skill->img): ?>
                                <img src="<?php echo e(Storage::url($skill->img)); ?>"
                                     alt="<?php echo e($skill->name); ?>"
                                     class="w-5 h-5 object-contain"
                                >
                            <?php endif; ?>
                            <span class="text-sm font-medium text-gray-700"><?php echo e($skill->name); ?></span>
                        </div>
                        <span class="text-sm font-medium text-rose-500"><?php echo e($skill->proficiency); ?>%</span>
                    </div>

                    <div class="relative skill-bar-container">
                        <!-- Modern Progress Bar -->
                        <div class="h-2 bg-gray-100 rounded-full overflow-hidden backdrop-blur-sm border border-gray-200/50">
                            <div class="skill-progress h-full relative rounded-full"
                                 style="--progress: <?php echo e($skill->proficiency); ?>%"
                                 data-skill="<?php echo e($skill->name); ?>"
                                 data-proficiency="<?php echo e($skill->proficiency); ?>">
                                <!-- Glass Effect Background -->
                                <div class="absolute inset-0
                                    <?php switch(strtolower($skill->name)):
                                        case ('javascript'): ?>
                                            bg-gradient-to-r from-yellow-400/90 to-yellow-300/90
                                            <?php break; ?>
                                        <?php case ('php'): ?>
                                            bg-gradient-to-r from-indigo-600/90 to-indigo-500/90
                                            <?php break; ?>
                                        <?php case ('python'): ?>
                                            bg-gradient-to-r from-blue-500/90 to-yellow-500/90
                                            <?php break; ?>
                                        <?php case ('html'): ?>
                                            bg-gradient-to-r from-orange-600/90 to-orange-500/90
                                            <?php break; ?>
                                        <?php case ('css'): ?>
                                            bg-gradient-to-r from-blue-500/90 to-blue-400/90
                                            <?php break; ?>
                                        <?php case ('react'): ?>
                                            bg-gradient-to-r from-cyan-500/90 to-cyan-400/90
                                            <?php break; ?>
                                        <?php case ('vue'): ?>
                                            bg-gradient-to-r from-emerald-500/90 to-emerald-400/90
                                            <?php break; ?>
                                        <?php case ('angular'): ?>
                                            bg-gradient-to-r from-red-600/90 to-red-500/90
                                            <?php break; ?>
                                        <?php case ('laravel'): ?>
                                            bg-gradient-to-r from-red-500/90 to-red-400/90
                                            <?php break; ?>
                                        <?php case ('nodejs'): ?>
                                            bg-gradient-to-r from-green-600/90 to-green-500/90
                                            <?php break; ?>
                                        <?php case ('mysql'): ?>
                                            bg-gradient-to-r from-blue-600/90 to-blue-500/90
                                            <?php break; ?>
                                        <?php case ('postgresql'): ?>
                                            bg-gradient-to-r from-blue-700/90 to-blue-600/90
                                            <?php break; ?>
                                        <?php case ('mongodb'): ?>
                                            bg-gradient-to-r from-green-500/90 to-green-400/90
                                            <?php break; ?>
                                        <?php case ('docker'): ?>
                                            bg-gradient-to-r from-blue-500/90 to-blue-400/90
                                            <?php break; ?>
                                        <?php case ('git'): ?>
                                            bg-gradient-to-r from-orange-600/90 to-orange-500/90
                                            <?php break; ?>
                                        <?php case ('typescript'): ?>
                                            bg-gradient-to-r from-blue-600/90 to-blue-500/90
                                            <?php break; ?>
                                        <?php case ('sass'): ?>
                                            bg-gradient-to-r from-pink-500/90 to-pink-400/90
                                            <?php break; ?>
                                        <?php case ('less'): ?>
                                            bg-gradient-to-r from-indigo-500/90 to-indigo-400/90
                                            <?php break; ?>
                                        <?php case ('tailwind'): ?>
                                            bg-gradient-to-r from-cyan-500/90 to-cyan-400/90
                                            <?php break; ?>
                                        <?php case ('bootstrap'): ?>
                                            bg-gradient-to-r from-purple-600/90 to-purple-500/90
                                            <?php break; ?>
                                        <?php case ('jquery'): ?>
                                            bg-gradient-to-r from-blue-500/90 to-blue-400/90
                                            <?php break; ?>
                                        <?php case ('react native'): ?>
                                            bg-gradient-to-r from-blue-500/90 to-blue-400/90
                                            <?php break; ?>
                                        <?php case ('flutter'): ?>
                                            bg-gradient-to-r from-blue-400/90 to-cyan-400/90
                                            <?php break; ?>
                                        <?php case ('swift'): ?>
                                            bg-gradient-to-r from-orange-500/90 to-orange-400/90
                                            <?php break; ?>
                                        <?php case ('kotlin'): ?>
                                            bg-gradient-to-r from-purple-500/90 to-purple-400/90
                                            <?php break; ?>
                                        <?php case ('java'): ?>
                                            bg-gradient-to-r from-red-700/90 to-red-600/90
                                            <?php break; ?>
                                        <?php case ('c#'): ?>
                                            bg-gradient-to-r from-purple-700/90 to-purple-600/90
                                            <?php break; ?>
                                        <?php case ('ruby'): ?>
                                            bg-gradient-to-r from-red-600/90 to-red-500/90
                                            <?php break; ?>
                                        <?php case ('ruby on rails'): ?>
                                            bg-gradient-to-r from-red-600/90 to-red-500/90
                                            <?php break; ?>
                                        <?php case ('go'): ?>
                                            bg-gradient-to-r from-cyan-500/90 to-cyan-400/90
                                            <?php break; ?>
                                        <?php case ('rust'): ?>
                                            bg-gradient-to-r from-orange-700/90 to-orange-600/90
                                            <?php break; ?>
                                        <?php case ('next.js'): ?>
                                            bg-gradient-to-r from-gray-900/90 to-gray-800/90
                                            <?php break; ?>
                                        <?php case ('nuxt.js'): ?>
                                            bg-gradient-to-r from-green-500/90 to-green-400/90
                                            <?php break; ?>
                                        <?php case ('svelte'): ?>
                                            bg-gradient-to-r from-red-500/90 to-orange-500/90
                                            <?php break; ?>
                                        <?php case ('django'): ?>
                                            bg-gradient-to-r from-green-700/90 to-green-600/90
                                            <?php break; ?>
                                        <?php case ('flask'): ?>
                                            bg-gradient-to-r from-gray-800/90 to-gray-700/90
                                            <?php break; ?>
                                        <?php case ('fastapi'): ?>
                                            bg-gradient-to-r from-teal-500/90 to-teal-400/90
                                            <?php break; ?>
                                        <?php case ('express'): ?>
                                            bg-gradient-to-r from-gray-600/90 to-gray-500/90
                                            <?php break; ?>
                                        <?php case ('nest.js'): ?>
                                            bg-gradient-to-r from-red-600/90 to-red-500/90
                                            <?php break; ?>
                                        <?php case ('graphql'): ?>
                                            bg-gradient-to-r from-pink-600/90 to-pink-500/90
                                            <?php break; ?>
                                        <?php case ('redis'): ?>
                                            bg-gradient-to-r from-red-600/90 to-red-500/90
                                            <?php break; ?>
                                        <?php case ('elasticsearch'): ?>
                                            bg-gradient-to-r from-cyan-500/90 to-blue-500/90
                                            <?php break; ?>
                                        <?php case ('aws'): ?>
                                            bg-gradient-to-r from-orange-500/90 to-orange-400/90
                                            <?php break; ?>
                                        <?php case ('azure'): ?>
                                            bg-gradient-to-r from-blue-600/90 to-blue-500/90
                                            <?php break; ?>
                                        <?php case ('google cloud'): ?>
                                            bg-gradient-to-r from-red-500/90 to-blue-500/90
                                            <?php break; ?>
                                        <?php case ('firebase'): ?>
                                            bg-gradient-to-r from-yellow-500/90 to-orange-500/90
                                            <?php break; ?>
                                        <?php case ('kubernetes'): ?>
                                            bg-gradient-to-r from-blue-600/90 to-blue-500/90
                                            <?php break; ?>
                                        <?php case ('jenkins'): ?>
                                            bg-gradient-to-r from-red-500/90 to-red-400/90
                                            <?php break; ?>
                                        <?php case ('webpack'): ?>
                                            bg-gradient-to-r from-blue-500/90 to-blue-400/90
                                            <?php break; ?>
                                        <?php case ('vite'): ?>
                                            bg-gradient-to-r from-purple-600/90 to-purple-500/90
                                            <?php break; ?>
                                        <?php default: ?>
                                            bg-gradient-to-r from-indigo-500/90 to-violet-400/90
                                    <?php endswitch; ?>
                                    backdrop-blur">
                                </div>
                                <!-- Pulse Effect -->
                                <div class="pulse-effect"></div>
                            </div>
                        </div>
                        <!-- Ripple Container -->
                        <div class="ripple-container absolute inset-0"></div>
                    </div>
                </div>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </div>
        <?php else: ?>
            <div class="text-center py-12">
                <div class="bg-white rounded-lg shadow-sm p-8 max-w-2xl mx-auto">
                    <svg class="w-16 h-16 text-gray-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"/>
                    </svg>
                    <h3 class="text-lg font-medium text-gray-900 mb-2">No Skills Added Yet</h3>
                    <p class="text-gray-600">Skills and expertise will appear here once they are added to your portfolio.</p>
                </div>
            </div>
        <?php endif; ?>
    </div>
</section>

<?php $__env->startPush('styles'); ?>
<style>
    .skill-progress {
        width: 0;
        transition: none;
    }

    .pulse-effect {
        position: absolute;
        inset: 0;
        background: linear-gradient(90deg,
            transparent 0%,
            rgba(255, 255, 255, 0.6) 50%,
            transparent 100%);
        animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
    }

    @keyframes pulse {
        0%, 100% {
            opacity: 0;
            transform: translateX(-100%);
        }
        50% {
            opacity: 1;
            transform: translateX(100%);
        }
    }

    .skill-item {
        transition: all 0.3s ease;
    }

    .skill-item:hover .skill-progress {
        filter: brightness(1.1) contrast(1.1);
        transform: scale(1.01);
    }
</style>
<?php $__env->stopPush(); ?>

<?php $__env->startPush('scripts'); ?>
<script>
document.addEventListener('DOMContentLoaded', function() {
    const observer = new IntersectionObserver((entries) => {
        entries.forEach((entry) => {
            if (entry.isIntersecting) {
                const skillItem = entry.target;
                const progress = skillItem.querySelector('.skill-progress');
                const valueDisplay = skillItem.querySelector('.text-rose-500');
                const targetValue = parseInt(progress.dataset.proficiency);

                let currentValue = 0;
                valueDisplay.textContent = '0%';

                // Smooth animation function
                const animate = () => {
                    // Increment by small steps
                    const increment = targetValue / 60; // Adjust for smoothness (60fps)
                    currentValue = Math.min(currentValue + increment, targetValue);

                    // Update both progress bar and number
                    progress.style.width = `${currentValue}%`;
                    valueDisplay.textContent = `${Math.round(currentValue)}%`;

                    // Continue animation if not reached target
                    if (currentValue < targetValue) {
                        requestAnimationFrame(animate);
                    }
                };

                // Start animation
                requestAnimationFrame(animate);
            }
        });
    }, { threshold: 0.2 });

    document.querySelectorAll('.skill-item').forEach(item => observer.observe(item));

    // Interactive features
    document.querySelectorAll('.skill-bar-container').forEach(container => {
        const progress = container.querySelector('.skill-progress');
        const details = container.querySelector('.skill-details p');
        const skillName = progress.dataset.skill;
        const proficiency = progress.dataset.proficiency;

        // Mouse move effect
        container.addEventListener('mousemove', (e) => {
            // Update details position
            const detailsElem = container.querySelector('.skill-details');
            detailsElem.style.opacity = '1';
            details.textContent = `${skillName}: ${proficiency}%`;
        });

        // Mouse leave effect
        container.addEventListener('mouseleave', () => {
            progress.style.transform = 'perspective(1000px) rotateX(0) rotateY(0) scale(1)';
            container.querySelector('.skill-details').style.opacity = '0';
        });

        // Click effect
        container.addEventListener('click', (e) => {
            const ripple = document.createElement('div');
            ripple.className = 'ripple';
            ripple.style.background = getRippleColor(skillName);
            const rect = container.getBoundingClientRect();
            const x = e.clientX - rect.left;
            const y = e.clientY - rect.top;
            ripple.style.left = `${x}px`;
            ripple.style.top = `${y}px`;
            container.querySelector('.ripple-container').appendChild(ripple);

            // Add active class
            container.classList.add('active');

            // Remove ripple after animation
            setTimeout(() => ripple.remove(), 600);

            // Remove active class
            setTimeout(() => container.classList.remove('active'), 300);

            // Trigger particle burst
            container.querySelectorAll('.particle').forEach(particle => {
                particle.style.animationName = 'none';
                requestAnimationFrame(() => {
                    particle.style.animationName = 'moveParticle';
                });
            });
        });
    });
});
</script>
<?php $__env->stopPush(); ?>
<?php /**PATH D:\Project Code\portfolio\resources\views/front/partials/skills.blade.php ENDPATH**/ ?>