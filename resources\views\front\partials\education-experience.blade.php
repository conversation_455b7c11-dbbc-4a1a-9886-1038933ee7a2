<section class="lg:py-10 py-5 relative overflow-hidden">
    <!-- Background Elements -->
    <div class="absolute inset-0" style="z-index: -1;">
        <div class="absolute inset-0 bg-gradient-to-br from-gray-50 to-white"></div>
        <div class="absolute inset-0 bg-[linear-gradient(to_right,#80808012_1px,transparent_1px),linear-gradient(to_bottom,#80808012_1px,transparent_1px)] bg-[size:24px_24px]"></div>
    </div>

    <div class="max-w-[1200px] mx-auto px-4 relative">
        <!-- Section Header -->
        <div class="text-center mb-4">
            <div class="inline-flex items-center gap-2 px-4 py-2 rounded-full bg-white/80 backdrop-blur shadow-lg mb-4">
                <span class="relative flex h-2 w-2">
                    <span class="animate-ping absolute inline-flex h-full w-full rounded-full bg-violet-400 opacity-75"></span>
                    <span class="relative inline-flex rounded-full h-2 w-2 bg-violet-500"></span>
                </span>
                <span class="text-sm font-medium bg-gradient-to-r from-violet-600 to-indigo-600 bg-clip-text text-transparent">
                    Experience & Education
                </span>
            </div>
            <h2 class="lg:text-3xl text-2xl font-bold text-gray-900 mb-2">My Journey</h2>
            <p class="text-gray-600 max-w-2xl mx-auto">
                My journey in the world of technology and programming
            </p>
        </div>

        <!-- Two Column Layout -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 lg:gap-12">
            <!-- Education Column -->
            <div class="relative">
                <h3 class="text-2xl font-bold text-gray-800 mb-3 text-center">Education</h3>
                <div class="relative">
                    <!-- Timeline Line -->
                    <div class="absolute left-4 top-0 h-full w-0.5 bg-gradient-to-b from-violet-200 via-indigo-200 to-transparent" style="z-index: 0;"></div>

                    <!-- Timeline Items -->
                    <div class="space-y-8">
                        @if($profile->user->education()->count() > 0)
                            @foreach($profile->user->education()->orderBy('start_date', 'desc')->get() as $index => $education)
                            <div class="relative group" data-aos="fade-up" data-aos-delay="{{ $index * 100 }}">
                                <!-- Timeline Dot -->
                                <div class="absolute left-4 top-2 -translate-x-1/2 w-3 h-3" style="z-index: 2;">
                                    <div class="w-full h-full rounded-full border-2 border-white bg-violet-400
                                              shadow-md group-hover:scale-110 transition-transform duration-300"></div>
                                </div>

                                <!-- Date Badge -->
                                <div class="ml-12">
                                    <div class="inline-flex items-center gap-2 px-4 py-1.5 rounded-full bg-white shadow-md group-hover:shadow-lg transition-all duration-300">
                                        <span class="relative flex h-2.5 w-2.5">
                                            <span class="animate-ping absolute inline-flex h-full w-full rounded-full bg-violet-400 opacity-75"></span>
                                            <span class="relative inline-flex rounded-full h-2.5 w-2.5 bg-violet-500"></span>
                                        </span>
                                        <span class="text-sm font-medium text-gray-700">
                                            {{ $education->start_date->format('M Y') }} - {{ $education->end_date ? $education->end_date->format('M Y') : 'Present' }}
                                        </span>
                                    </div>
                                </div>

                                <!-- Content Card -->
                                <div class="ml-12 mt-3">
                                    <div class="bg-white p-3 rounded-2xl shadow-lg border border-violet-500
                                              hover:-translate-y-1 hover:shadow-xl transition-all duration-300">
                                        <div class="flex items-center gap-4">
                                            @if($education->logo)
                                                <img src="{{ Storage::url($education->logo) }}" alt="{{ $education->institution }}"
                                                     class="w-12 h-12 rounded-full object-contain bg-gray-50 p-2">
                                            @endif
                                            <div class="">
                                                <h4 class="text-base lg:text-lg font-bold text-gray-900 group-hover:text-violet-600 transition-colors">
                                                    {{ $education->institution }}
                                                </h4>
                                                <p class="text-violet-600 font-medium">{{ $education->degree }}</p>
                                                <p class="text-gray-600 text-sm">{{ $education->field_of_study }}</p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            @endforeach
                        @else
                            <div class="relative group" data-aos="fade-up">
                                <div class="ml-12">
                                    <div class="bg-white p-6 rounded-2xl shadow-lg border border-violet-500 text-center">
                                        <p class="text-gray-600">Education history not updated yet</p>
                                    </div>
                                </div>
                            </div>
                        @endif
                    </div>
                </div>
            </div>

            <!-- Experience Column -->
            <div class="relative">
                <h3 class="text-2xl font-bold text-gray-800 mb-3 text-center">Experience</h3>
                <div class="relative">
                    <!-- Timeline Line -->
                    <div class="absolute left-4 top-0 h-full w-0.5 bg-gradient-to-b from-indigo-200 via-violet-200 to-transparent" style="z-index: 0;"></div>

                    <!-- Timeline Items -->
                    <div class="space-y-8">
                        @if($profile->user->experiences()->count() > 0)
                            @foreach($profile->user->experiences()->orderBy('start_date', 'desc')->get() as $index => $experience)
                            <div class="relative group" data-aos="fade-up" data-aos-delay="{{ $index * 100 }}">
                                <!-- Timeline Dot -->
                                <div class="absolute left-4 top-2 -translate-x-1/2 w-3 h-3" style="z-index: 2;">
                                    <div class="w-full h-full rounded-full border-2 border-white bg-indigo-400
                                              shadow-md group-hover:scale-110 transition-transform duration-300"></div>
                                </div>

                                <!-- Date Badge -->
                                <div class="ml-12">
                                    <div class="inline-flex items-center gap-2 px-4 py-1.5 rounded-full bg-white shadow-md group-hover:shadow-lg transition-all duration-300">
                                        <span class="relative flex h-2.5 w-2.5">
                                            <span class="animate-ping absolute inline-flex h-full w-full rounded-full bg-indigo-400 opacity-75"></span>
                                            <span class="relative inline-flex rounded-full h-2.5 w-2.5 bg-indigo-500"></span>
                                        </span>
                                        <span class="text-sm font-medium text-gray-700">
                                            {{ $experience->start_date->format('M Y') }} - {{ $experience->end_date ? $experience->end_date->format('M Y') : 'Present' }}
                                        </span>
                                    </div>
                                </div>

                                <!-- Content Card -->
                                <div class="ml-12 mt-3">
                                    <div class="bg-white p-3 rounded-2xl shadow-lg border border-indigo-500
                                              hover:-translate-y-1 hover:shadow-xl transition-all duration-300">
                                        <div class="flex items-center gap-4">
                                            @if($experience->logo)
                                                <img src="{{ Storage::url($experience->logo) }}" alt="{{ $experience->company }}"
                                                     class="w-12 h-12 rounded-full object-contain bg-gray-50 p-2">
                                            @endif
                                            <div class="">
                                                <h4 class="text-base lg:text-lg font-bold text-gray-900 group-hover:text-indigo-600 transition-colors">
                                                    {{ $experience->title }}
                                                </h4>
                                                <p class="text-indigo-600 font-medium">{{ $experience->company }}</p>
                                                <p class="text-gray-600 text-sm">{{ $experience->description }}</p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            @endforeach
                        @else
                            <div class="relative group" data-aos="fade-up">
                                <div class="ml-12">
                                    <div class="bg-white p-6 rounded-2xl shadow-lg border border-indigo-500 text-center">
                                        <p class="text-gray-600">Work experience not updated yet</p>
                                    </div>
                                </div>
                            </div>
                        @endif
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
