<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Casts\Attribute;

class Service extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'user_id',
        'name',
        'image',
        'description',
        'price_range',
        'pricing_type',
        'delivery_time',
        'category',
        'is_active',
    ];

    protected $casts = [
        'price_range' => 'decimal:0',
        'is_active' => 'boolean',
    ];

    // Menambahkan accessor untuk formatted price
    protected function formattedPrice(): Attribute
    {
        return Attribute::make(
            get: function () {
                return 'Rp ' . number_format($this->price_range, 0, ',', '.');
            }
        );
    }

    // Pricing type constants
    const PRICING_TYPE_FIXED = 'fixed';
    const PRICING_TYPE_HOURLY = 'hourly';
    const PRICING_TYPE_PROJECT = 'project';

    // Delivery time constants
    const DELIVERY_TIMES = [
        '1-3 days',
        '3-7 days',
        '1-2 weeks',
        '2-4 weeks',
        '1-2 months',
        'custom'
    ];

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function hireRequests(): HasMany
    {
        return $this->hasMany(HireRequest::class);
    }

    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    // Get available pricing types
    public static function getPricingTypes(): array
    {
        return [
            self::PRICING_TYPE_HOURLY => 'Per Hour',
            self::PRICING_TYPE_PROJECT => 'Project Based',
        ];
    }

    // Get available delivery times
    public static function getDeliveryTimes(): array
    {
        return array_combine(self::DELIVERY_TIMES, self::DELIVERY_TIMES);
    }

    // Helper method untuk format harga
    public static function formatPrice($price): string
    {
        return 'Rp ' . number_format($price, 0, ',', '.');
    }
}