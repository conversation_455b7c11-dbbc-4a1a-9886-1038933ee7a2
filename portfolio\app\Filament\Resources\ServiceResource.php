<?php

namespace App\Filament\Resources;

use App\Filament\Resources\ServiceResource\Pages;
use App\Models\Service;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use Illuminate\Database\Eloquent\Collection;

class ServiceResource extends Resource
{
    protected static ?string $model = Service::class;
    protected static ?string $navigationIcon = 'heroicon-o-wrench-screwdriver';
    protected static ?string $navigationGroup = 'Business';
    protected static ?int $navigationSort = 7;

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Group::make()
                    ->schema([
                        Forms\Components\Section::make('Basic Information')
                            ->schema([
                                Forms\Components\FileUpload::make('image')
                                    ->image()
                                    ->directory('services')
                                    ->imageResizeMode('cover')
                                    ->imageCropAspectRatio('16:9')
                                    ->imageResizeTargetWidth('800')
                                    ->imageResizeTargetHeight('450')
                                    ->label('Service Image'),
                                Forms\Components\TextInput::make('name')
                                    ->required()
                                    ->maxLength(255)
                                    ->label('Service Name')
                                    ->placeholder('e.g. Website Development'),
                                Forms\Components\RichEditor::make('description')
                                    ->required()
                                    ->maxLength(65535)
                                    ->placeholder('Describe your service in detail')
                                    ->columnSpanFull(),
                            ]),
                        Forms\Components\Section::make('Pricing & Delivery')
                            ->schema([
                                Forms\Components\TextInput::make('price_range')
                                    ->required()
                                    ->prefix('Rp')
                                    ->numeric()
                                    ->step(1000)
                                    ->inputMode('decimal')
                                    ->placeholder('700000')
                                    ->label('Starting Price')
                                    ->helperText('Minimum price in Rupiah (e.g. 700000 for Rp 700.000)')
                                    ->live()
                                    ->afterStateUpdated(function ($state, callable $set) {
                                        if ($state) {
                                            $set('price_formatted', 'Rp ' . number_format($state, 0, ',', '.'));
                                        }
                                    }),
                                Forms\Components\TextInput::make('price_formatted')
                                    ->label('Formatted Price')
                                    ->disabled()
                                    ->dehydrated(false),
                                Forms\Components\Select::make('pricing_type')
                                    ->options([
                                        'hourly' => 'Per Hour',
                                        'project' => 'Project Based',
                                    ])
                                    ->required()
                                    ->default('project')
                                    ->label('Pricing Type'),
                                Forms\Components\Select::make('delivery_time')
                                    ->options([
                                        '1-3 days' => '1-3 Days',
                                        '3-7 days' => '3-7 Days',
                                        '1-2 weeks' => '1-2 Weeks',
                                        '2-4 weeks' => '2-4 Weeks',
                                        '1-2 months' => '1-2 Months',
                                        'custom' => 'Custom Timeline',
                                    ])
                                    ->required()
                                    ->label('Estimated Delivery Time'),
                            ]),
                    ])
                    ->columnSpan(['lg' => 2]),
                Forms\Components\Group::make()
                    ->schema([
                        Forms\Components\Section::make('Status & Category')
                            ->schema([
                                Forms\Components\Toggle::make('is_active')
                                    ->label('Active Status')
                                    ->helperText('Inactive services are not visible to clients')
                                    ->default(true),
                                Forms\Components\Select::make('category')
                                    ->options([
                                        'Web Development' => 'Web Development',
                                        'Mobile Development' => 'Mobile Development',
                                        'UI/UX Design' => 'UI/UX Design',
                                        'Digital Marketing' => 'Digital Marketing',
                                        'Content Writing' => 'Content Writing',
                                        'Graphic Design' => 'Graphic Design',
                                        'Other' => 'Other',
                                    ])
                                    ->searchable()
                                    ->required()
                                    ->label('Service Category'),
                            ]),
                    ])
                    ->columnSpan(['lg' => 1]),
            ])
            ->columns(3);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\ImageColumn::make('image')
                    ->square()
                    ->defaultImageUrl(url('/images/placeholder.jpg')),
                Tables\Columns\TextColumn::make('name')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('category')
                    ->badge()
                    ->sortable(),
                Tables\Columns\TextColumn::make('price_range')
                    ->formatStateUsing(fn (string $state): string => 'Rp ' . number_format($state, 0, ',', '.'))
                    ->sortable(),
                Tables\Columns\TextColumn::make('delivery_time')
                    ->sortable(),
                Tables\Columns\IconColumn::make('is_active')
                    ->boolean()
                    ->sortable(),
            ])
            ->filters([
                Tables\Filters\TrashedFilter::make(),
                Tables\Filters\SelectFilter::make('category')
                    ->options([
                        'Web Development' => 'Web Development',
                        'Mobile Development' => 'Mobile Development',
                        'UI/UX Design' => 'UI/UX Design',
                        'Digital Marketing' => 'Digital Marketing',
                        'Content Writing' => 'Content Writing',
                        'Graphic Design' => 'Graphic Design',
                        'Other' => 'Other',
                    ]),
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                    Tables\Actions\BulkAction::make('toggleActive')
                        ->label('Toggle Active')
                        ->icon('heroicon-o-eye')
                        ->action(fn (Collection $records) => $records->each->update(['is_active' => !$records->first()->is_active])),
                ]),
            ])
            ->defaultSort('created_at', 'desc');
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListServices::route('/'),
            'create' => Pages\CreateService::route('/create'),
            'edit' => Pages\EditService::route('/{record}/edit'),
        ];
    }

    public static function getEloquentQuery(): Builder
    {
        return parent::getEloquentQuery()
            ->where('user_id', auth()->id())
            ->withoutGlobalScopes([
                SoftDeletingScope::class,
            ]);
    }
}