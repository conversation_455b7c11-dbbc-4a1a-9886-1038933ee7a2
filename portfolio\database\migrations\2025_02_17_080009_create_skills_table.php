<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('skills', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->cascadeOnDelete();
            $table->string('name');
            $table->string('img')->nullable(); // For skill icon image
            $table->string('type')->default('technology'); // technology, language, soft_skill, etc
            $table->integer('proficiency')->default(0); // 0-100
            $table->boolean('is_core_technology')->default(false);
            $table->boolean('is_highlighted')->default(false);
            $table->boolean('is_project')->default(false);
            $table->integer('order')->default(0);
            $table->timestamps();

            // Memastikan kombinasi name dan user_id unik
            $table->unique(['name', 'user_id']);
        });

        Schema::create('profile_skill', function (Blueprint $table) {
            $table->id();
            $table->foreignId('profile_id')->constrained()->cascadeOnDelete();
            $table->foreignId('skill_id')->constrained()->cascadeOnDelete();
            $table->integer('order')->default(0); // Untuk pengurutan dalam profile
            $table->timestamps();

            // Memastikan skill tidak duplikat dalam satu profile
            $table->unique(['profile_id', 'skill_id']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('profile_skill');
        Schema::dropIfExists('skills');
    }
};
