<?php

namespace App\Http\Controllers;

use App\Models\Profile;
use App\Models\Project;
use App\Models\Service;
use App\Models\Skill;
use App\Models\Experience;
use App\Models\Certification;
use App\Models\SocialMedia;
use App\Models\HireRequest;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;

class FrontController extends Controller
{
    public function index()
    {
        // Ambil user pertama atau yang sedang login
        $user = auth()->user() ?? User::first();

        if (!$user) {
            // Jika tidak ada user sama sekali, buat default profile tanpa user
            $profile = new Profile([
                'title' => 'Welcome to My Portfolio',
                'availability_status' => 'available',
                'availability_text' => 'Available for Work',
            ]);
        } else {
            // Jika ada user, cari atau buat profile dengan user_id
            $profile = Profile::firstOrCreate(
                ['user_id' => $user->id],
                [
                    'title' => 'Welcome to My Portfolio',
                    'availability_status' => 'available',
                    'availability_text' => 'Available for Work',
                    'user_id' => $user->id,
                ]
            );
        }

        return view('front.index', [
            'profile' => $profile,
            'projects' => Project::latest()->get(),
            'services' => Service::all(),
            'skills' => Skill::all(),
            'experiences' => Experience::latest('start_date')->get(),
            'certifications' => Certification::latest('issue_date')->get(),
            'social_media' => SocialMedia::all(),
        ]);
    }

    public function about()
    {
        return view('front.about');
    }

    public function services()
    {
        $services = Service::active()->latest()->paginate(9);
        return view('front.services.index', compact('services'));
    }

    public function serviceShow(Service $service)
    {
        return view('front.services.show', compact('service'));
    }

    public function portfolio()
    {
        $projects = Project::latest()->paginate(9);
        return view('front.portfolio.index', compact('projects'));
    }

    public function projectShow(Project $project)
    {
        return view('front.portfolio.show', compact('project'));
    }

    public function contact()
    {
        return view('front.contact');
    }

    public function storeHireRequest(Request $request)
    {
        $validated = $request->validate([
            'service_id' => 'required|exists:services,id',
            'name' => 'required|string|max:255',
            'email' => 'required|email|max:255',
            'phone' => 'required|string|max:255',
            'project_description' => 'required|string',
            'budget' => 'required|string|max:255',
            'deadline' => 'required|date',
            'reference_file' => 'nullable|file|max:10240', // Max 10MB
        ]);

        if ($request->hasFile('reference_file')) {
            $path = $request->file('reference_file')->store('hire-requests', 'public');
            $validated['reference_file'] = $path;
        }

        // Set status default
        $validated['status'] = 'pending';

        $hireRequest = HireRequest::create($validated);

        if ($request->ajax()) {
            return response()->json([
                'message' => 'Your request has been submitted successfully! We will contact you soon.',
                'status' => 'success'
            ]);
        }

        return back()->with('success', 'Your request has been submitted successfully! We will contact you soon.');
    }

    public function serviceHireForm(Service $service)
    {
        return view('front.services.hire', [
            'service' => $service,
            'profile' => Profile::first(),
        ]);
    }

    public function projects()
    {
        $profile = Profile::with(['user.projects' => function ($query) {
            $query->latest();
        }])->firstOrFail();

        return view('front.projects', compact('profile'));
    }
}