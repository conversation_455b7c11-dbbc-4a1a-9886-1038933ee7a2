<?php

use App\Http\Controllers\ProfileController;
use App\Http\Controllers\FrontController;
use Illuminate\Http\RedirectResponse;
use Illuminate\Support\Facades\Route;
use Illuminate\Support\Facades\Artisan;


// storage
Route::get('/storage-link', function () {
    Artisan::call('storage:link');
    return 'Storage link created successfully';
});

Route::get('/', function () {
    return view('welcome');
});

Route::get('/dashboard', function (): RedirectResponse {
    return redirect('/admin');
})->middleware(['auth', 'verified'])->name('dashboard');

Route::middleware('auth')->group(function () {
    Route::get('/profile', [ProfileController::class, 'edit'])->name('profile.edit');
    Route::patch('/profile', [ProfileController::class, 'update'])->name('profile.update');
    Route::delete('/profile', [ProfileController::class, 'destroy'])->name('profile.destroy');
});

Route::get('/', [FrontController::class, 'index'])->name('home');
Route::get('/about', [FrontController::class, 'about'])->name('about');
Route::get('/services', [FrontController::class, 'services'])->name('services');
Route::get('/services/{service}', [FrontController::class, 'serviceShow'])->name('services.show');
Route::get('/portfolio', [FrontController::class, 'portfolio'])->name('portfolio');
Route::get('/portfolio/{project}', [FrontController::class, 'projectShow'])->name('portfolio.show');
Route::get('/contact', [FrontController::class, 'contact'])->name('contact');
Route::get('/projects', [FrontController::class, 'projects'])->name('projects');

Route::post('/hire-requests', [FrontController::class, 'storeHireRequest'])->name('hire-requests.store');
Route::get('/services/{service}/hire', [FrontController::class, 'serviceHireForm'])->name('service.hire');

require __DIR__.'/auth.php';