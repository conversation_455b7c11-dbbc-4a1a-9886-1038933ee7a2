<section id="hire" class="lg:py-10 py-5 relative overflow-hidden">
    <!-- Background Elements -->
    <div class="absolute inset-0" style="z-index: -1;">
        <div class="absolute inset-0 bg-gradient-to-br from-gray-50 to-white"></div>
        <div class="absolute inset-0 bg-[linear-gradient(to_right,#80808012_1px,transparent_1px),linear-gradient(to_bottom,#80808012_1px,transparent_1px)] bg-[size:24px_24px]"></div>
    </div>

    <div class="max-w-[1200px] mx-auto px-4">
        <div class="lg:grid lg:grid-cols-2 lg:gap-12 items-start">
            <!-- Left Column: Info & Lottie -->
            <div class="lg:sticky ">
                <!-- Section Header -->
                <div class="mb-4 lg:text-left text-center">
                    <div class="inline-flex items-center gap-2 px-4 py-2 rounded-full bg-white/80 backdrop-blur shadow-lg mb-4">
                        <span class="relative flex h-2 w-2">
                            <span class="animate-ping absolute inline-flex h-full w-full rounded-full bg-emerald-400 opacity-75"></span>
                            <span class="relative inline-flex rounded-full h-2 w-2 bg-emerald-500"></span>
                        </span>
                        <span class="text-sm font-medium bg-gradient-to-r from-emerald-600 to-teal-600 bg-clip-text text-transparent">
                            Let's Work Together
                        </span>
                    </div>
                    <h2 class="lg:text-3xl text-2xl font-bold text-gray-900 mb-2">Start Your Project</h2>
                    <p class="text-gray-600 lg:max-w-none max-w-sm mx-auto">
                        Tell me about your project and I'll help bring your vision to life with the best solutions and practices
                    </p>
                </div>

                <!-- Features/Benefits -->
                <div class="space-y-4 mb-8">
                    <div class="flex items-start gap-3">
                        <div class="flex-shrink-0">
                            <div class="p-2 bg-emerald-50 rounded-lg">
                                <svg class="w-4 h-4 text-emerald-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                </svg>
                            </div>
                        </div>
                        <div>
                            <h3 class="font-medium text-gray-900">Professional Service</h3>
                            <p class="text-sm text-gray-600">Get high-quality work delivered on time</p>
                        </div>
                    </div>
                    <div class="flex items-start gap-3">
                        <div class="flex-shrink-0">
                            <div class="p-2 bg-emerald-50 rounded-lg">
                                <svg class="w-4 h-4 text-emerald-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                </svg>
                            </div>
                        </div>
                        <div>
                            <h3 class="font-medium text-gray-900">Quick Response</h3>
                            <p class="text-sm text-gray-600">Get response within 24 hours</p>
                        </div>
                    </div>

                    <div class="flex items-start gap-3">
                        <div class="flex-shrink-0">
                            <div class="p-2 bg-emerald-50 rounded-lg">
                                <svg class="w-4 h-4 text-emerald-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"></path>
                                </svg>
                            </div>
                        </div>
                        <div>
                            <h3 class="font-medium text-gray-900">Secure Process</h3>
                            <p class="text-sm text-gray-600">Your data and project details are fully protected</p>
                        </div>
                    </div>

                    <div class="flex items-start gap-3">
                        <div class="flex-shrink-0">
                            <div class="p-2 bg-emerald-50 rounded-lg">
                                <svg class="w-4 h-4 text-emerald-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 8h2a2 2 0 012 2v6a2 2 0 01-2 2h-2v4l-4-4H9a1.994 1.994 0 01-1.414-.586m0 0L11 14h4a2 2 0 002-2V6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2v4l.586-.586z"></path>
                                </svg>
                            </div>
                        </div>
                        <div>
                            <h3 class="font-medium text-gray-900">Clear Communication</h3>
                            <p class="text-sm text-gray-600">Regular updates and open discussion throughout the project</p>
                        </div>
                    </div>

                    <div class="flex items-start gap-3">
                        <div class="flex-shrink-0">
                            <div class="p-2 bg-emerald-50 rounded-lg">
                                <svg class="w-4 h-4 text-emerald-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                                </svg>
                            </div>
                        </div>
                        <div>
                            <h3 class="font-medium text-gray-900">Fast Delivery</h3>
                            <p class="text-sm text-gray-600">Efficient workflow to meet your deadlines</p>
                        </div>
                    </div>

                    <div class="flex items-start gap-3">
                        <div class="flex-shrink-0">
                            <div class="p-2 bg-emerald-50 rounded-lg">
                                <svg class="w-4 h-4 text-emerald-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
                                </svg>
                            </div>
                        </div>
                        <div>
                            <h3 class="font-medium text-gray-900">Dedicated Support</h3>
                            <p class="text-sm text-gray-600">Post-project support and maintenance available</p>
                        </div>
                    </div>
                </div>


            </div>

            <!-- Right Column: Form -->
            <div class="mt-8">
                <div class="bg-white rounded-xl shadow-lg p-5 border border-emerald-500 lg:w-auto mx-auto">
                    <form id="hireFormIndex" action="<?php echo e(route('hire-requests.store')); ?>" method="POST" enctype="multipart/form-data" class="space-y-3">
                        <?php echo csrf_field(); ?>

                        <!-- Service Selection -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700">Select Service</label>
                            <select name="service_id" required
                                    class="mt-1 block w-full rounded-lg border-gray-300 shadow-sm text-sm
                                           focus:border-emerald-500 focus:ring-emerald-500 h-9">
                                <option value="">Choose a service...</option>
                                <?php $__empty_1 = true; $__currentLoopData = $services->where('is_active', true); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $service): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                                    <option value="<?php echo e($service->id); ?>"><?php echo e($service->name); ?></option>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                                    <option value="" disabled>No services available</option>
                                <?php endif; ?>
                            </select>
                        </div>

                        <!-- Two Column Layout -->
                        <div class="grid grid-cols-2 gap-3">
                            <!-- Name -->
                            <div>
                                <label class="block text-sm font-medium text-gray-700">Name</label>
                                <input type="text" name="name" required
                                       class="mt-1 block w-full rounded-lg border-gray-300 shadow-sm text-sm
                                              focus:border-emerald-500 focus:ring-emerald-500 h-9">
                            </div>

                            <!-- Email -->
                            <div>
                                <label class="block text-sm font-medium text-gray-700">Email</label>
                                <input type="email" name="email" required
                                       class="mt-1 block w-full rounded-lg border-gray-300 shadow-sm text-sm
                                              focus:border-emerald-500 focus:ring-emerald-500 h-9">
                            </div>
                        </div>

                        <div class="grid grid-cols-2 gap-3">
                            <!-- Phone -->
                            <div>
                                <label class="block text-sm font-medium text-gray-700">Phone</label>
                                <input type="tel" name="phone" required
                                       class="mt-1 block w-full rounded-lg border-gray-300 shadow-sm text-sm
                                              focus:border-emerald-500 focus:ring-emerald-500 h-9">
                            </div>

                            <!-- Budget -->
                            <div>
                                <label class="block text-sm font-medium text-gray-700">Budget</label>
                                <input type="text" name="budget" required
                                       placeholder="e.g. 1-2 million"
                                       class="mt-1 block w-full rounded-lg border-gray-300 shadow-sm text-sm
                                              focus:border-emerald-500 focus:ring-emerald-500 h-9">
                            </div>
                        </div>

                        <!-- Project Description -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700">Project Description</label>
                            <textarea name="project_description" rows="2" required
                                      class="mt-1 block w-full rounded-lg border-gray-300 shadow-sm text-sm
                                             focus:border-emerald-500 focus:ring-emerald-500"
                                      placeholder="Tell me about your project..."></textarea>
                        </div>

                        <!-- Deadline -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700">Deadline</label>
                            <input type="date" name="deadline" required
                                   class="mt-1 block w-full rounded-lg border-gray-300 shadow-sm text-sm
                                          focus:border-emerald-500 focus:ring-emerald-500 h-9">
                        </div>

                        <!-- Reference File -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700">Reference File (Optional)</label>
                            <input type="file" name="reference_file"
                                   class="mt-1 block w-full text-sm text-gray-500
                                          file:mr-3 file:py-1.5 file:px-3
                                          file:rounded-full file:border-0
                                          file:text-xs file:font-medium
                                          file:bg-emerald-50 file:text-emerald-600
                                          hover:file:bg-emerald-100">
                            <p class="mt-0.5 text-xs text-gray-500">Max. 10MB</p>
                        </div>

                        <!-- Submit Button -->
                        <div>
                            <button type="submit"
                                    class="w-full flex justify-center items-center gap-2 py-2 px-4 rounded-lg
                                           bg-gradient-to-r from-emerald-600 to-teal-600
                                           text-white text-sm font-medium shadow-lg
                                           hover:shadow-xl transition duration-300
                                           focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-emerald-500">
                                <span>Send Request</span>
                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                          d="M14 5l7 7m0 0l-7 7m7-7H3"/>
                                </svg>
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</section>

<?php $__env->startPush('scripts'); ?>

<!-- Existing Scripts -->
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
<script>
function showAlert(type, title, message) {
    const container = document.getElementById('alertContainer');
    const titleEl = document.getElementById('alertTitle');
    const messageEl = document.getElementById('alertMessage');
    const successIcon = document.querySelector('.success-icon');
    const errorIcon = document.querySelector('.error-icon');

    // Set content
    titleEl.textContent = title;
    messageEl.textContent = message;

    // Set type-specific styles
    if (type === 'success') {
        container.classList.add('border-emerald-500');
        container.classList.remove('border-red-500');
        successIcon.classList.remove('hidden');
        errorIcon.classList.add('hidden');
    } else {
        container.classList.add('border-red-500');
        container.classList.remove('border-emerald-500');
        successIcon.classList.add('hidden');
        errorIcon.classList.remove('hidden');
    }

    // Show alert
    container.classList.remove('translate-x-full');
    container.classList.add('translate-x-0');

    // Auto hide after 5 seconds
    setTimeout(hideAlert, 5000);
}

function hideAlert() {
    const container = document.getElementById('alertContainer');
    container.classList.remove('translate-x-0');
    container.classList.add('translate-x-full');
}

document.addEventListener('DOMContentLoaded', function() {
    const hireForm = document.getElementById('hireFormIndex');

    if (hireForm) {
        hireForm.addEventListener('submit', function(e) {
            e.preventDefault();
            console.log('Form submitted'); // Debug log

            const submitButton = this.querySelector('button[type="submit"]');
            const formData = new FormData(this);

            // Disable button and show loading
            submitButton.disabled = true;
            const originalButtonContent = submitButton.innerHTML;
            submitButton.innerHTML = `
                <svg class="animate-spin h-5 w-5 text-white inline" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                    <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                <span class="ml-2">Sending...</span>
            `;

            // Send the form
            fetch(this.action, {
                method: 'POST',
                body: formData,
                headers: {
                    'X-Requested-With': 'XMLHttpRequest',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
                }
            })
            .then(response => response.json())
            .then(data => {
                console.log('Response:', data); // Debug log
                if (data.status === 'success') {
                    this.reset();
                    Swal.fire({
                        title: 'Success!',
                        text: 'Your request has been submitted successfully! We will contact you soon.',
                        icon: 'success',
                        confirmButtonColor: '#059669',
                        confirmButtonText: 'Great!',
                        timer: 3000,
                        timerProgressBar: true,
                        showCloseButton: true,
                        customClass: {
                            popup: 'animate__animated animate__fadeInDown'
                        }
                    });
                } else {
                    throw new Error(data.message || 'Something went wrong');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                Swal.fire({
                    title: 'Oops!',
                    text: 'Something went wrong. Please try again.',
                    icon: 'error',
                    confirmButtonColor: '#DC2626',
                    confirmButtonText: 'Try Again',
                    showCloseButton: true,
                    customClass: {
                        popup: 'animate__animated animate__fadeInDown'
                    }
                });
            })
            .finally(() => {
                // Re-enable button and restore content
                submitButton.disabled = false;
                submitButton.innerHTML = originalButtonContent;
            });
        });
    } else {
        console.error('Hire form not found!'); // Debug log
    }
});
</script>
<?php $__env->stopPush(); ?>
<?php /**PATH D:\Project Code\portfolio\resources\views/front/partials/hire-form.blade.php ENDPATH**/ ?>