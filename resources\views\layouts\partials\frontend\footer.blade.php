<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
    <div class="grid grid-cols-1 md:grid-cols-4 gap-8">
        <!-- About Section -->
        <div class="col-span-1">
            <h3 class="text-lg font-semibold mb-2 text-primary-500">About</h3>
            <p class="text-gray-300">Welcome to my professional portfolio and services platform. Here, I showcase my work, expertise, and projects while connecting with potential clients.</p>
        </div>

        <!-- Quick Links -->
        <div class="col-span-1">
            <h3 class="text-lg font-semibold mb-2 text-primary-500">Quick Links</h3>
            <ul class="space-y-2">
                <li><a href="#" onclick="scrollToSection('top', event)" class="text-gray-300 hover:text-primary-400">Home</a></li>
                <li><a href="#services" onclick="scrollToSection('services', event)" class="text-gray-300 hover:text-primary-400">Services</a></li>
                <li><a href="#projects" onclick="scrollToSection('projects', event)" class="text-gray-300 hover:text-primary-400">Projects</a></li>
                <li><a href="#certifications" onclick="scrollToSection('certifications', event)" class="text-gray-300 hover:text-primary-400">Certifications</a></li>
                <li><a href="#hire" onclick="scrollToSection('hire', event)" class="text-gray-300 hover:text-primary-400">Contact</a></li>
            </ul>
        </div>

        <!-- Services -->
        <div class="col-span-1">
            <h3 class="text-lg font-semibold mb-2 text-primary-500">Services</h3>
            <ul class="space-y-2">
                @forelse($services ?? [] as $service)
                <li>
                    <a href="#services"
                        onclick="scrollToSection('services', event)"
                        class="text-gray-300 hover:text-primary-400">
                        {{ $service->name }}
                    </a>
                </li>
                @empty
                <li class="text-gray-300">No services available</li>
                @endforelse
            </ul>
        </div>

        <!-- Contact Info -->
        <div class="col-span-1">
            <h3 class="text-lg font-semibold mb-4 text-primary-500">Contact Info</h3>
            <ul class="space-y-2">
                @php
                // Ambil user pertama atau admin sebagai default contact
                $defaultUser = \App\Models\User::with('profile')->first();
                @endphp

                @if($defaultUser && $defaultUser->email)
                <li class="flex items-center text-gray-300">
                    <i class="fas fa-envelope w-5 mr-2"></i>
                    <a href="mailto:{{ $defaultUser->email }}" class="hover:text-primary-400">
                        {{ $defaultUser->email }}
                    </a>
                </li>
                @endif

                @if($defaultUser && $defaultUser->profile && $defaultUser->profile->phone)
                <li class="flex items-center text-gray-300">
                    <i class="fas fa-phone w-5 mr-2"></i>
                    <a href="tel:{{ $defaultUser->profile->phone }}" class="hover:text-primary-400">
                        {{ $defaultUser->profile->phone }}
                    </a>
                </li>
                @endif

                @if($defaultUser && $defaultUser->profile && $defaultUser->profile->address)
                <li class="flex items-center text-gray-300">
                    <i class="fas fa-map-marker-alt w-5 mr-2"></i>
                    <span>{{ $defaultUser->profile->address }}</span>
                </li>
                @endif
            </ul>
        </div>
    </div>

    <div class="border-t border-gray-800 mt-12 pt-8">
        <div class="flex flex-col md:flex-row justify-between items-center">
            <p class="text-gray-300">&copy; {{ date('Y') }} Deni Suryadi. All rights reserved.</p>
            <div class="flex space-x-6 mt-4 md:mt-0">
                @foreach(App\Models\SocialMedia::all() as $social)
                <a href="{{ $social->url }}"
                    target="_blank"
                    rel="noopener noreferrer"
                    class="text-gray-300 hover:text-primary-400">
                    <i class="fab fa-{{ $social->platform }}"></i>
                </a>
                @endforeach
            </div>
        </div>
    </div>
</div>
