<?php

namespace App\Filament\Resources;

use App\Filament\Resources\SocialMediaResource\Pages;
use App\Models\SocialMedia;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class SocialMediaResource extends Resource
{
    protected static ?string $model = SocialMedia::class;
    protected static ?string $navigationIcon = 'heroicon-o-share';
    protected static ?string $navigationGroup = 'Personal Information';
    protected static ?int $navigationSort = 9;

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Select::make('user_id')
                    ->relationship('user', 'name')
                    ->required(),
                Forms\Components\Select::make('platform')
                    ->options([
                        'github' => 'GitHub',
                        'linkedin' => 'LinkedIn',
                        'twitter' => 'Twitter',
                        'instagram' => 'Instagram',
                        'facebook' => 'Facebook',
                        'youtube' => 'YouTube',
                        'medium' => 'Medium',
                        'dev.to' => 'Dev.to',
                        'dribbble' => 'Dribbble',
                        'behance' => 'Behance',
                        'other' => 'Other',
                    ])
                    ->required(),
                Forms\Components\TextInput::make('url')
                    ->url()
                    ->required()
                    ->maxLength(255)
                    ->columnSpanFull(),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('platform')
                    ->searchable()
                    ->badge()
                    ->color(fn (string $state): string => match ($state) {
                        'github' => 'gray',
                        'linkedin' => 'info',
                        'twitter' => 'info',
                        'instagram' => 'warning',
                        'facebook' => 'info',
                        'youtube' => 'danger',
                        'medium' => 'gray',
                        'dev.to' => 'gray',
                        'dribbble' => 'pink',
                        'behance' => 'blue',
                        default => 'gray',
                    }),
                Tables\Columns\TextColumn::make('url')
                    ->searchable()
                    ->limit(50),
                Tables\Columns\TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                Tables\Filters\TrashedFilter::make(),
                Tables\Filters\SelectFilter::make('platform')
                    ->options([
                        'github' => 'GitHub',
                        'linkedin' => 'LinkedIn',
                        'twitter' => 'Twitter',
                        'instagram' => 'Instagram',
                        'facebook' => 'Facebook',
                        'youtube' => 'YouTube',
                        'medium' => 'Medium',
                        'dev.to' => 'Dev.to',
                        'dribbble' => 'Dribbble',
                        'behance' => 'Behance',
                        'other' => 'Other',
                    ]),
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
                Tables\Actions\ForceDeleteAction::make(),
                Tables\Actions\RestoreAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                    Tables\Actions\ForceDeleteBulkAction::make(),
                    Tables\Actions\RestoreBulkAction::make(),
                ]),
            ]);
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListSocialMedia::route('/'),
            'create' => Pages\CreateSocialMedia::route('/create'),
            'edit' => Pages\EditSocialMedia::route('/{record}/edit'),
        ];
    }

    public static function getEloquentQuery(): Builder
    {
        return parent::getEloquentQuery()
            ->where('user_id', auth()->id())
            ->withoutGlobalScopes([
                SoftDeletingScope::class,
            ]);
    }
}