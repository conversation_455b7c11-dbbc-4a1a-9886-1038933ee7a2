<section id="certifications" class="lg:py-10 py-5 relative overflow-hidden">
    <!-- Background Elements -->
    <div class="absolute inset-0" style="z-index: -1;">
        <div class="absolute inset-0 bg-gradient-to-br from-gray-50 to-white"></div>
        <div class="absolute inset-0 bg-[linear-gradient(to_right,#80808012_1px,transparent_1px),linear-gradient(to_bottom,#80808012_1px,transparent_1px)] bg-[size:24px_24px]"></div>
    </div>

    <div class="max-w-[1200px] mx-auto px-4">
        <!-- Section Header -->
        <div class="text-center mb-4">
            <div class="inline-flex items-center gap-2 px-4 py-2 rounded-full bg-white/80 backdrop-blur shadow-lg mb-4">
                <span class="relative flex h-2 w-2">
                    <span class="animate-ping absolute inline-flex h-full w-full rounded-full bg-indigo-500 opacity-75"></span>
                    <span class="relative inline-flex rounded-full h-2 w-2 bg-indigo-500"></span>
                </span>
                <span class="text-sm font-medium bg-gradient-to-r from-indigo-500 to-indigo-600 bg-clip-text text-transparent">
                    Professional Growth
                </span>
            </div>
            <h2 class="lg:text-3xl text-2xl font-bold text-gray-900 mb-2">Certifications</h2>
            <p class="text-gray-600 max-w-2xl mx-auto">
                Professional certifications and achievements that validate my expertise
            </p>
        </div>

        @php
            $certifications = $profile->user->certifications()->latest('issue_date')->get();
        @endphp

        @if($certifications->isNotEmpty())
            <!-- Certifications Slider -->
            <div class="splide" id="certifications-slider">
                <div class="splide__track">
                    <div class="splide__list">
                        @foreach($certifications as $certification)
                        <div class="splide__slide p-4" data-aos="fade-up" data-aos-delay="{{ $loop->index * 100 }}">
                            <div class="bg-white rounded-2xl shadow-md hover:shadow-xl transition-all duration-300 overflow-hidden h-full flex flex-col">
                                <!-- Certificate Image -->
                                <div class="relative">
                                    <img src="{{ Storage::url($certification->image_url) }}"
                                         alt="{{ $certification->title }}"
                                         class="aspect-video w-full object-cover">

                                    <!-- Badges Container -->
                                    <div class="absolute top-4 left-4 right-4 flex justify-between z-10">
                                        <div class="flex flex-wrap gap-2">
                                            <span class="px-3 py-1.5 text-xs font-medium rounded-full shadow-sm backdrop-blur-sm bg-indigo-100/90 text-indigo-600">
                                                {{ $certification->issuer }}
                                            </span>
                                            <span class="px-3 py-1.5 text-xs font-medium rounded-full shadow-sm backdrop-blur-sm bg-indigo-100/90 text-indigo-600">
                                                {{ $certification->issue_date->format('d M Y') }}
                                            </span>
                                        </div>
                                    </div>
                                </div>

                                <!-- Content -->
                                <div class="p-6 flex-grow flex flex-col">
                                    <h3 class="text-xl font-semibold text-gray-900 mb-3">
                                        {{ $certification->title }}
                                    </h3>

                                    <!-- View Certificate Button -->
                                    @if($certification->credential_url)
                                    <div class="mt-auto pt-4 border-t border-gray-100">
                                        <a href="{{ $certification->credential_url }}"
                                           target="_blank"
                                           class="inline-flex items-center gap-2 text-sm font-medium text-indigo-600 hover:text-indigo-700 transition-colors">
                                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                      d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14"/>
                                            </svg>
                                            <span>View Certificate</span>
                                        </a>
                                    </div>
                                    @endif
                                </div>
                            </div>
                        </div>
                        @endforeach
                    </div>
                </div>
            </div>
        @else
            <div class="text-center py-12">
                <div class="bg-white rounded-lg shadow-sm p-8 max-w-2xl mx-auto">
                    <svg class="w-16 h-16 text-gray-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"/>
                    </svg>
                    <h3 class="text-lg font-medium text-gray-900 mb-2">No Certifications Yet</h3>
                    <p class="text-gray-600">Certifications will appear here once they are added to your portfolio.</p>
                </div>
            </div>
        @endif
    </div>
</section>

@push('styles')
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/@splidejs/splide@4.1.4/dist/css/splide.min.css">
<link rel="stylesheet" href="https://unpkg.com/aos@next/dist/aos.css" />
@endpush

@push('scripts')
<script src="https://cdn.jsdelivr.net/npm/@splidejs/splide@4.1.4/dist/js/splide.min.js"></script>
<script src="https://unpkg.com/aos@next/dist/aos.js"></script>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Initialize certifications slider
        new Splide('#certifications-slider', {
            perPage: 3,
            gap: '2rem',
            breakpoints: {
                1024: {
                    perPage: 2,
                },
                640: {
                    perPage: 1,
                }
            },
            pagination: true,
            arrows: true,
        }).mount();

        // Initialize AOS
        AOS.init({
            once: true,
            offset: 50,
            duration: 800,
            easing: 'ease-in-out',
            delay: 100
        });

        // Refresh AOS after dynamic content loads
        setTimeout(function() {
            AOS.refresh();
        }, 500);
    });
</script>
@endpush
