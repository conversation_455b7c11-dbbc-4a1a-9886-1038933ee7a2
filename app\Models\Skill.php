<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;

class Skill extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'name',
        'img',
        'type',
        'proficiency',
        'order',
        'is_core_technology',
        'is_highlighted',
        'is_project'
    ];

    protected $casts = [
        'is_core_technology' => 'boolean',
        'is_highlighted' => 'boolean',
        'is_project' => 'boolean',
        'proficiency' => 'integer'
    ];

    // Relations
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function profiles(): BelongsToMany
    {
        return $this->belongsToMany(Profile::class, 'profile_skill')
                    ->withTimestamps()
                    ->withPivot('order')
                    ->orderBy('profile_skill.order');
    }

    public function projects(): BelongsToMany
    {
        return $this->belongsToMany(Project::class, 'project_skill')
                    ->withTimestamps()
                    ->withPivot('order')
                    ->orderBy('project_skill.order');
    }

    // Scopes
    public function scopeForUser($query, $userId = null)
    {
        return $query->where('user_id', $userId ?? auth()->id());
    }

    public function scopeCoreTechnologies($query)
    {
        return $query->where('is_core_technology', true);
    }

    public function scopeHighlighted($query)
    {
        return $query->where('is_highlighted', true);
    }

    public function scopeProjectSkills($query)
    {
        return $query->where('is_project', true);
    }

    public function scopeByType($query, $type)
    {
        return $query->where('type', $type);
    }
}