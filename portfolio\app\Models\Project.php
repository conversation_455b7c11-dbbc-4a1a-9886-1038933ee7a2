<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;

class Project extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'user_id',
        'title',
        'description',
        'image_url',
        'project_duration',
        'client',
        'type',
        'features',
        'preview_url',
        'gallery',
        'start_date',
        'end_date'
    ];

    protected $casts = [
        'features' => 'array',
        'gallery' => 'array',
        'start_date' => 'date',
        'end_date' => 'date'
    ];

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function skills(): BelongsToMany
    {
        return $this->belongsToMany(Skill::class, 'project_skill')
                    ->withTimestamps()
                    ->withPivot('order')
                    ->orderBy('project_skill.order');
    }

    // Scope untuk mendapatkan project berdasarkan user
    public function scopeForUser($query, $userId = null)
    {
        return $query->where('user_id', $userId ?? auth()->id());
    }

    // Scope untuk mendapatkan project berdasarkan tipe
    public function scopeByType($query, $type)
    {
        return $query->where('type', $type);
    }

    // Accessor untuk durasi project dalam format yang lebih baik
    public function getProjectPeriodAttribute()
    {
        if (!$this->start_date) {
            return null;
        }

        if (!$this->end_date) {
            return $this->start_date->format('M Y') . ' - Present';
        }

        $days = $this->start_date->diffInDays($this->end_date);
        $years = floor($days / 365);
        $remainingDays = $days % 365;
        $months = floor($remainingDays / 30);
        $remainingDays = $remainingDays % 30;
        $weeks = floor($remainingDays / 7);
        $remainingDays = $remainingDays % 7;

        $parts = [];

        if ($years > 0) {
            $parts[] = $years . ' ' . str('year')->plural($years);
        }

        if ($months > 0) {
            $parts[] = $months . ' ' . str('month')->plural($months);
        }

        if ($weeks > 0) {
            $parts[] = $weeks . ' ' . str('week')->plural($weeks);
        }

        if ($remainingDays > 0 && count($parts) === 0) {
            $parts[] = $remainingDays . ' ' . str('day')->plural($remainingDays);
        }

        return count($parts) > 0 ? implode(', ', $parts) : 'Less than a day';
    }

    // Accessor untuk client name yang aman
    public function getClientNameAttribute()
    {
        return $this->type === 'client' ? $this->client : 'Personal Project';
    }
}
